(function(){"use strict";var n={};!function(){n.d=function(b,L){for(var P in L){if(n.o(L,P)&&!n.o(b,P)){Object.defineProperty(b,P,{enumerable:true,get:L[P]})}}}}();!function(){n.o=function(n,b){return Object.prototype.hasOwnProperty.call(n,b)}}();!function(){n.r=function(n){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(n,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(n,"__esModule",{value:true})}}();if(typeof n!=="undefined")n.ab=__dirname+"/";var b={};n.r(b);n.d(b,{CLSThresholds:function(){return j},FCPThresholds:function(){return B},FIDThresholds:function(){return cn},INPThresholds:function(){return nn},LCPThresholds:function(){return en},TTFBThresholds:function(){return rn},onCLS:function(){return w},onFCP:function(){return S},onFID:function(){return $},onINP:function(){return N},onLCP:function(){return z},onTTFB:function(){return K}});var L,P,I,A,F,D=-1,a=function(n){addEventListener("pageshow",(function(b){b.persisted&&(D=b.timeStamp,n(b))}),!0)},c=function(){var n=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(n&&n.responseStart>0&&n.responseStart<performance.now())return n},u=function(){var n=c();return n&&n.activationStart||0},f=function(n,b){var L=c(),P="navigate";D>=0?P="back-forward-cache":L&&(document.prerendering||u()>0?P="prerender":document.wasDiscarded?P="restore":L.type&&(P=L.type.replace(/_/g,"-")));return{name:n,value:void 0===b?-1:b,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:P}},s=function(n,b,L){try{if(PerformanceObserver.supportedEntryTypes.includes(n)){var P=new PerformanceObserver((function(n){Promise.resolve().then((function(){b(n.getEntries())}))}));return P.observe(Object.assign({type:n,buffered:!0},L||{})),P}}catch(n){}},d=function(n,b,L,P){var I,A;return function(F){b.value>=0&&(F||P)&&((A=b.value-(I||0))||void 0===I)&&(I=b.value,b.delta=A,b.rating=function(n,b){return n>b[1]?"poor":n>b[0]?"needs-improvement":"good"}(b.value,L),n(b))}},l=function(n){requestAnimationFrame((function(){return requestAnimationFrame((function(){return n()}))}))},p=function(n){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&n()}))},v=function(n){var b=!1;return function(){b||(n(),b=!0)}},O=-1,h=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},g=function(n){"hidden"===document.visibilityState&&O>-1&&(O="visibilitychange"===n.type?n.timeStamp:0,T())},y=function(){addEventListener("visibilitychange",g,!0),addEventListener("prerenderingchange",g,!0)},T=function(){removeEventListener("visibilitychange",g,!0),removeEventListener("prerenderingchange",g,!0)},E=function(){return O<0&&(O=h(),y(),a((function(){setTimeout((function(){O=h(),y()}),0)}))),{get firstHiddenTime(){return O}}},C=function(n){document.prerendering?addEventListener("prerenderingchange",(function(){return n()}),!0):n()},B=[1800,3e3],S=function(n,b){b=b||{},C((function(){var L,P=E(),I=f("FCP"),A=s("paint",(function(n){n.forEach((function(n){"first-contentful-paint"===n.name&&(A.disconnect(),n.startTime<P.firstHiddenTime&&(I.value=Math.max(n.startTime-u(),0),I.entries.push(n),L(!0)))}))}));A&&(L=d(n,I,B,b.reportAllChanges),a((function(P){I=f("FCP"),L=d(n,I,B,b.reportAllChanges),l((function(){I.value=performance.now()-P.timeStamp,L(!0)}))})))}))},j=[.1,.25],w=function(n,b){b=b||{},S(v((function(){var L,P=f("CLS",0),I=0,A=[],c=function(n){n.forEach((function(n){if(!n.hadRecentInput){var b=A[0],L=A[A.length-1];I&&n.startTime-L.startTime<1e3&&n.startTime-b.startTime<5e3?(I+=n.value,A.push(n)):(I=n.value,A=[n])}})),I>P.value&&(P.value=I,P.entries=A,L())},F=s("layout-shift",c);F&&(L=d(n,P,j,b.reportAllChanges),p((function(){c(F.takeRecords()),L(!0)})),a((function(){I=0,P=f("CLS",0),L=d(n,P,j,b.reportAllChanges),l((function(){return L()}))})),setTimeout(L,0))})))},x=0,_=1/0,G=0,M=function(n){n.forEach((function(n){n.interactionId&&(_=Math.min(_,n.interactionId),G=Math.max(G,n.interactionId),x=G?(G-_)/7+1:0)}))},k=function(){"interactionCount"in performance||L||(L=s("event",M,{type:"event",buffered:!0,durationThreshold:0}))},J=[],Q=new Map,U=0,R=function(){return(L?x:performance.interactionCount||0)-U},Z=[],H=function(n){if(Z.forEach((function(b){return b(n)})),n.interactionId||"first-input"===n.entryType){var b=J[J.length-1],L=Q.get(n.interactionId);if(L||J.length<10||n.duration>b.latency){if(L)n.duration>L.latency?(L.entries=[n],L.latency=n.duration):n.duration===L.latency&&n.startTime===L.entries[0].startTime&&L.entries.push(n);else{var P={id:n.interactionId,latency:n.duration,entries:[n]};Q.set(P.id,P),J.push(P)}J.sort((function(n,b){return b.latency-n.latency})),J.length>10&&J.splice(10).forEach((function(n){return Q.delete(n.id)}))}}},q=function(n){var b=self.requestIdleCallback||self.setTimeout,L=-1;return n=v(n),"hidden"===document.visibilityState?n():(L=b(n),p(n)),L},nn=[200,500],N=function(n,b){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(b=b||{},C((function(){var L;k();var P,I=f("INP"),o=function(n){q((function(){n.forEach(H);var b,L=(b=Math.min(J.length-1,Math.floor(R()/50)),J[b]);L&&L.latency!==I.value&&(I.value=L.latency,I.entries=L.entries,P())}))},A=s("event",o,{durationThreshold:null!==(L=b.durationThreshold)&&void 0!==L?L:40});P=d(n,I,nn,b.reportAllChanges),A&&(A.observe({type:"first-input",buffered:!0}),p((function(){o(A.takeRecords()),P(!0)})),a((function(){U=0,J.length=0,Q.clear(),I=f("INP"),P=d(n,I,nn,b.reportAllChanges)})))})))},en=[2500,4e3],tn={},z=function(n,b){b=b||{},C((function(){var L,P=E(),I=f("LCP"),o=function(n){b.reportAllChanges||(n=n.slice(-1)),n.forEach((function(n){n.startTime<P.firstHiddenTime&&(I.value=Math.max(n.startTime-u(),0),I.entries=[n],L())}))},A=s("largest-contentful-paint",o);if(A){L=d(n,I,en,b.reportAllChanges);var F=v((function(){tn[I.id]||(o(A.takeRecords()),A.disconnect(),tn[I.id]=!0,L(!0))}));["keydown","click"].forEach((function(n){addEventListener(n,(function(){return q(F)}),!0)})),p(F),a((function(P){I=f("LCP"),L=d(n,I,en,b.reportAllChanges),l((function(){I.value=performance.now()-P.timeStamp,tn[I.id]=!0,L(!0)}))}))}}))},rn=[800,1800],on=function e(n){document.prerendering?C((function(){return e(n)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(n)}),!0):setTimeout(n,0)},K=function(n,b){b=b||{};var L=f("TTFB"),P=d(n,L,rn,b.reportAllChanges);on((function(){var I=c();I&&(L.value=Math.max(I.responseStart-u(),0),L.entries=[I],P(!0),a((function(){L=f("TTFB",0),(P=d(n,L,rn,b.reportAllChanges))(!0)})))}))},an={passive:!0,capture:!0},un=new Date,V=function(n,b){P||(P=b,I=n,A=new Date,Y(removeEventListener),W())},W=function(){if(I>=0&&I<A-un){var n={entryType:"first-input",name:P.type,target:P.target,cancelable:P.cancelable,startTime:P.timeStamp,processingStart:P.timeStamp+I};F.forEach((function(b){b(n)})),F=[]}},X=function(n){if(n.cancelable){var b=(n.timeStamp>1e12?new Date:performance.now())-n.timeStamp;"pointerdown"==n.type?function(n,b){var t=function(){V(n,b),i()},r=function(){i()},i=function(){removeEventListener("pointerup",t,an),removeEventListener("pointercancel",r,an)};addEventListener("pointerup",t,an),addEventListener("pointercancel",r,an)}(b,n):V(b,n)}},Y=function(n){["mousedown","keydown","touchstart","pointerdown"].forEach((function(b){return n(b,X,an)}))},cn=[100,300],$=function(n,b){b=b||{},C((function(){var L,A=E(),D=f("FID"),l=function(n){n.startTime<A.firstHiddenTime&&(D.value=n.processingStart-n.startTime,D.entries.push(n),L(!0))},m=function(n){n.forEach(l)},O=s("first-input",m);L=d(n,D,cn,b.reportAllChanges),O&&(p(v((function(){m(O.takeRecords()),O.disconnect()}))),a((function(){var A;D=f("FID"),L=d(n,D,cn,b.reportAllChanges),F=[],I=-1,P=null,Y(addEventListener),A=l,F.push(A),W()})))}))};module.exports=b})();