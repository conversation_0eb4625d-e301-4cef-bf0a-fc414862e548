{"version": 3, "sources": ["../../../src/build/next-config-ts/require-hook.ts"], "sourcesContent": ["import type { Options as SWCOptions } from '@swc/core'\nimport Module from 'node:module'\nimport { readFileSync } from 'node:fs'\nimport { dirname } from 'node:path'\n\nconst oldJSHook = require.extensions['.js']\nconst extensions = ['.ts', '.cts', '.mts', '.cjs', '.mjs']\n\nexport function registerHook(swcOptions: SWCOptions) {\n  // lazy require swc since it loads React before even setting NODE_ENV\n  // resulting loading Development React on Production\n  const { transformSync } = require('../swc')\n\n  require.extensions['.js'] = function (mod: any, oldFilename) {\n    try {\n      return oldJSHook(mod, oldFilename)\n    } catch (error) {\n      if ((error as NodeJS.ErrnoException).code !== 'ERR_REQUIRE_ESM') {\n        throw error\n      }\n\n      // calling oldJSHook throws ERR_REQUIRE_ESM, so run _compile manually\n      // TODO: investigate if we can remove readFileSync\n      const content = readFileSync(oldFilename, 'utf8')\n      const { code } = transformSync(content, swcOptions)\n      mod._compile(code, oldFilename)\n    }\n  }\n\n  for (const ext of extensions) {\n    const oldHook = require.extensions[ext] ?? oldJSHook\n    require.extensions[ext] = function (mod: any, oldFilename) {\n      const _compile = mod._compile\n\n      mod._compile = function (code: string, filename: string) {\n        const swc = transformSync(code, swcOptions)\n        return _compile.call(this, swc.code, filename)\n      }\n\n      return oldHook(mod, oldFilename)\n    }\n  }\n}\n\nexport function deregisterHook() {\n  require.extensions['.js'] = oldJSHook\n  extensions.forEach((ext) => delete require.extensions[ext])\n}\n\nexport function requireFromString(code: string, filename: string) {\n  const paths = (Module as any)._nodeModulePaths(dirname(filename))\n  const m = new Module(filename, module.parent!) as any\n  m.paths = paths\n  m._compile(code, filename)\n  return m.exports\n}\n"], "names": ["<PERSON><PERSON><PERSON>", "readFileSync", "dirname", "oldJSHook", "require", "extensions", "registerHook", "swcOptions", "transformSync", "mod", "oldFilename", "error", "code", "content", "_compile", "ext", "oldHook", "filename", "swc", "call", "deregisterHook", "for<PERSON>ach", "requireFromString", "paths", "_nodeModulePaths", "m", "module", "parent", "exports"], "mappings": "AACA,OAAOA,YAAY,cAAa;AAChC,SAASC,YAAY,QAAQ,UAAS;AACtC,SAASC,OAAO,QAAQ,YAAW;AAEnC,MAAMC,YAAYC,QAAQC,UAAU,CAAC,MAAM;AAC3C,MAAMA,aAAa;IAAC;IAAO;IAAQ;IAAQ;IAAQ;CAAO;AAE1D,OAAO,SAASC,aAAaC,UAAsB;IACjD,qEAAqE;IACrE,oDAAoD;IACpD,MAAM,EAAEC,aAAa,EAAE,GAAGJ,QAAQ;IAElCA,QAAQC,UAAU,CAAC,MAAM,GAAG,SAAUI,GAAQ,EAAEC,WAAW;QACzD,IAAI;YACF,OAAOP,UAAUM,KAAKC;QACxB,EAAE,OAAOC,OAAO;YACd,IAAI,AAACA,MAAgCC,IAAI,KAAK,mBAAmB;gBAC/D,MAAMD;YACR;YAEA,qEAAqE;YACrE,kDAAkD;YAClD,MAAME,UAAUZ,aAAaS,aAAa;YAC1C,MAAM,EAAEE,IAAI,EAAE,GAAGJ,cAAcK,SAASN;YACxCE,IAAIK,QAAQ,CAACF,MAAMF;QACrB;IACF;IAEA,KAAK,MAAMK,OAAOV,WAAY;QAC5B,MAAMW,UAAUZ,QAAQC,UAAU,CAACU,IAAI,IAAIZ;QAC3CC,QAAQC,UAAU,CAACU,IAAI,GAAG,SAAUN,GAAQ,EAAEC,WAAW;YACvD,MAAMI,WAAWL,IAAIK,QAAQ;YAE7BL,IAAIK,QAAQ,GAAG,SAAUF,IAAY,EAAEK,QAAgB;gBACrD,MAAMC,MAAMV,cAAcI,MAAML;gBAChC,OAAOO,SAASK,IAAI,CAAC,IAAI,EAAED,IAAIN,IAAI,EAAEK;YACvC;YAEA,OAAOD,QAAQP,KAAKC;QACtB;IACF;AACF;AAEA,OAAO,SAASU;IACdhB,QAAQC,UAAU,CAAC,MAAM,GAAGF;IAC5BE,WAAWgB,OAAO,CAAC,CAACN,MAAQ,OAAOX,QAAQC,UAAU,CAACU,IAAI;AAC5D;AAEA,OAAO,SAASO,kBAAkBV,IAAY,EAAEK,QAAgB;IAC9D,MAAMM,QAAQ,AAACvB,OAAewB,gBAAgB,CAACtB,QAAQe;IACvD,MAAMQ,IAAI,IAAIzB,OAAOiB,UAAUS,OAAOC,MAAM;IAC5CF,EAAEF,KAAK,GAAGA;IACVE,EAAEX,QAAQ,CAACF,MAAMK;IACjB,OAAOQ,EAAEG,OAAO;AAClB"}