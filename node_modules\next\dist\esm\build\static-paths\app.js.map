{"version": 3, "sources": ["../../../src/build/static-paths/app.ts"], "sourcesContent": ["import type { ParamValue, Params } from '../../server/request/params'\nimport type { AppPageModule } from '../../server/route-modules/app-page/module'\nimport type { AppSegment } from '../segment-config/app/app-segments'\nimport type { StaticPathsResult } from './types'\n\nimport path from 'path'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport { createWorkStore } from '../../server/async-storage/work-store'\nimport { FallbackMode } from '../../lib/fallback'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport {\n  getRouteRegex,\n  type RouteRegex,\n} from '../../shared/lib/router/utils/route-regex'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { normalizePathname, encodeParam } from './utils'\nimport escapePathDelimiters from '../../shared/lib/router/utils/escape-path-delimiters'\nimport { createIncrementalCache } from '../../export/helpers/create-incremental-cache'\nimport type { NextConfigComplete } from '../../server/config-shared'\n\n/**\n * Compares two parameters to see if they're equal.\n *\n * @param a - The first parameter.\n * @param b - The second parameter.\n * @returns Whether the parameters are equal.\n */\nfunction areParamValuesEqual(a: ParamValue, b: ParamValue) {\n  // If they're equal, then we can return true.\n  if (a === b) {\n    return true\n  }\n\n  // If they're both arrays, then we can return true if they have the same\n  // length and all the items are the same.\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) {\n      return false\n    }\n\n    return a.every((item, index) => item === b[index])\n  }\n\n  // Otherwise, they're not equal.\n  return false\n}\n\n/**\n * Filters out duplicate parameters from a list of parameters.\n *\n * @param routeParamKeys - The keys of the parameters.\n * @param routeParams - The list of parameters to filter.\n * @returns The list of unique parameters.\n */\nfunction filterUniqueParams(\n  routeParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const unique: Params[] = []\n\n  for (const params of routeParams) {\n    let i = 0\n    for (; i < unique.length; i++) {\n      const item = unique[i]\n      let j = 0\n      for (; j < routeParamKeys.length; j++) {\n        const key = routeParamKeys[j]\n\n        // If the param is not the same, then we need to break out of the loop.\n        if (!areParamValuesEqual(item[key], params[key])) {\n          break\n        }\n      }\n\n      // If we got to the end of the paramKeys array, then it means that we\n      // found a duplicate. Skip it.\n      if (j === routeParamKeys.length) {\n        break\n      }\n    }\n\n    // If we didn't get to the end of the unique array, then it means that we\n    // found a duplicate. Skip it.\n    if (i < unique.length) {\n      continue\n    }\n\n    unique.push(params)\n  }\n\n  return unique\n}\n\n/**\n * Filters out all combinations of root params from a list of parameters.\n *\n * Given the following root param ('lang'), and the following routeParams:\n *\n * ```\n * [\n *   { lang: 'en', region: 'US', slug: ['home'] },\n *   { lang: 'en', region: 'US', slug: ['about'] },\n *   { lang: 'fr', region: 'CA', slug: ['about'] },\n * ]\n * ```\n *\n * The result will be:\n *\n * ```\n * [\n *   { lang: 'en', region: 'US' },\n *   { lang: 'fr', region: 'CA' },\n * ]\n * ```\n *\n * @param rootParamKeys - The keys of the root params.\n * @param routeParams - The list of parameters to filter.\n * @returns The list of combinations of root params.\n */\nfunction filterRootParamsCombinations(\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const combinations: Params[] = []\n\n  for (const params of routeParams) {\n    const combination: Params = {}\n\n    // Collect all root params. As soon as we don't find a root param, break.\n    let i = 0\n    for (; i < rootParamKeys.length; i++) {\n      const key = rootParamKeys[i]\n      if (params[key]) {\n        combination[key] = params[key]\n      } else {\n        break\n      }\n    }\n\n    // If we didn't find all root params, skip this combination. We only want to\n    // generate combinations that have all root params.\n    if (i < rootParamKeys.length) {\n      continue\n    }\n\n    combinations.push(combination)\n  }\n\n  return combinations\n}\n\n/**\n * Validates the parameters to ensure they're accessible and have the correct\n * types.\n *\n * @param page - The page to validate.\n * @param regex - The route regex.\n * @param isRoutePPREnabled - Whether the route has partial prerendering enabled.\n * @param routeParamKeys - The keys of the parameters.\n * @param rootParamKeys - The keys of the root params.\n * @param routeParams - The list of parameters to validate.\n * @returns The list of validated parameters.\n */\nfunction validateParams(\n  page: string,\n  regex: RouteRegex,\n  isRoutePPREnabled: boolean,\n  routeParamKeys: readonly string[],\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const valid: Params[] = []\n\n  // Validate that if there are any root params, that the user has provided at\n  // least one value for them only if we're using partial prerendering.\n  if (isRoutePPREnabled && rootParamKeys.length > 0) {\n    if (\n      routeParams.length === 0 ||\n      rootParamKeys.some((key) =>\n        routeParams.some((params) => !(key in params))\n      )\n    ) {\n      if (rootParamKeys.length === 1) {\n        throw new Error(\n          `A required root parameter (${rootParamKeys[0]}) was not provided in generateStaticParams for ${page}, please provide at least one value.`\n        )\n      }\n\n      throw new Error(\n        `Required root params (${rootParamKeys.join(', ')}) were not provided in generateStaticParams for ${page}, please provide at least one value for each.`\n      )\n    }\n  }\n\n  for (const params of routeParams) {\n    const item: Params = {}\n\n    for (const key of routeParamKeys) {\n      const { repeat, optional } = regex.groups[key]\n\n      let paramValue = params[key]\n\n      if (\n        optional &&\n        params.hasOwnProperty(key) &&\n        (paramValue === null ||\n          paramValue === undefined ||\n          (paramValue as any) === false)\n      ) {\n        paramValue = []\n      }\n\n      // A parameter is missing, so the rest of the params are not accessible.\n      // We only support this when the route has partial prerendering enabled.\n      // This will make it so that the remaining params are marked as missing so\n      // we can generate a fallback route for them.\n      if (!paramValue && isRoutePPREnabled) {\n        break\n      }\n\n      // Perform validation for the parameter based on whether it's a repeat\n      // parameter or not.\n      if (repeat) {\n        if (!Array.isArray(paramValue)) {\n          throw new Error(\n            `A required parameter (${key}) was not provided as an array received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      } else {\n        if (typeof paramValue !== 'string') {\n          throw new Error(\n            `A required parameter (${key}) was not provided as a string received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      }\n\n      item[key] = paramValue\n    }\n\n    valid.push(item)\n  }\n\n  return valid\n}\n\n/**\n * Builds the static paths for an app using `generateStaticParams`.\n *\n * @param params - The parameters for the build.\n * @returns The static paths.\n */\nexport async function buildAppStaticPaths({\n  dir,\n  page,\n  distDir,\n  dynamicIO,\n  authInterrupts,\n  segments,\n  isrFlushToDisk,\n  cacheHandler,\n  cacheLifeProfiles,\n  requestHeaders,\n  cacheHandlers,\n  maxMemoryCacheSize,\n  fetchCacheKeyPrefix,\n  nextConfigOutput,\n  ComponentMod,\n  isRoutePPREnabled = false,\n  buildId,\n  rootParamKeys,\n}: {\n  dir: string\n  page: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  segments: AppSegment[]\n  distDir: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  cacheHandler?: string\n  cacheHandlers?: NextConfigComplete['experimental']['cacheHandlers']\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  ComponentMod: AppPageModule\n  isRoutePPREnabled: boolean\n  buildId: string\n  rootParamKeys: readonly string[]\n}): Promise<Partial<StaticPathsResult>> {\n  if (\n    segments.some((generate) => generate.config?.dynamicParams === true) &&\n    nextConfigOutput === 'export'\n  ) {\n    throw new Error(\n      '\"dynamicParams: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )\n  }\n\n  ComponentMod.patchFetch()\n\n  const incrementalCache = await createIncrementalCache({\n    dir,\n    distDir,\n    cacheHandler,\n    cacheHandlers,\n    requestHeaders,\n    fetchCacheKeyPrefix,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const regex = getRouteRegex(page)\n  const routeParamKeys = Object.keys(getRouteMatcher(regex)(page) || {})\n\n  const afterRunner = new AfterRunner()\n\n  const store = createWorkStore({\n    page,\n    // We're discovering the parameters here, so we don't have any unknown\n    // ones.\n    fallbackRouteParams: null,\n    renderOpts: {\n      incrementalCache,\n      cacheLifeProfiles,\n      supportsDynamicResponse: true,\n      isRevalidate: false,\n      experimental: {\n        dynamicIO,\n        authInterrupts,\n      },\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n    },\n    buildId,\n    previouslyRevalidatedTags: [],\n  })\n\n  const routeParams = await ComponentMod.workAsyncStorage.run(\n    store,\n    async () => {\n      async function builtRouteParams(\n        parentsParams: Params[] = [],\n        idx = 0\n      ): Promise<Params[]> {\n        // If we don't have any more to process, then we're done.\n        if (idx === segments.length) return parentsParams\n\n        const current = segments[idx]\n\n        if (\n          typeof current.generateStaticParams !== 'function' &&\n          idx < segments.length\n        ) {\n          return builtRouteParams(parentsParams, idx + 1)\n        }\n\n        const params: Params[] = []\n\n        if (current.generateStaticParams) {\n          // fetchCache can be used to inform the fetch() defaults used inside\n          // of generateStaticParams. revalidate and dynamic options don't come into\n          // play within generateStaticParams.\n          if (typeof current.config?.fetchCache !== 'undefined') {\n            store.fetchCache = current.config.fetchCache\n          }\n\n          if (parentsParams.length > 0) {\n            for (const parentParams of parentsParams) {\n              const result = await current.generateStaticParams({\n                params: parentParams,\n              })\n\n              for (const item of result) {\n                params.push({ ...parentParams, ...item })\n              }\n            }\n          } else {\n            const result = await current.generateStaticParams({ params: {} })\n\n            params.push(...result)\n          }\n        }\n\n        if (idx < segments.length) {\n          return builtRouteParams(params, idx + 1)\n        }\n\n        return params\n      }\n\n      return builtRouteParams()\n    }\n  )\n\n  let lastDynamicSegmentHadGenerateStaticParams = false\n  for (const segment of segments) {\n    // Check to see if there are any missing params for segments that have\n    // dynamicParams set to false.\n    if (\n      segment.param &&\n      segment.isDynamicSegment &&\n      segment.config?.dynamicParams === false\n    ) {\n      for (const params of routeParams) {\n        if (segment.param in params) continue\n\n        const relative = segment.filePath\n          ? path.relative(dir, segment.filePath)\n          : undefined\n\n        throw new Error(\n          `Segment \"${relative}\" exports \"dynamicParams: false\" but the param \"${segment.param}\" is missing from the generated route params.`\n        )\n      }\n    }\n\n    if (\n      segment.isDynamicSegment &&\n      typeof segment.generateStaticParams !== 'function'\n    ) {\n      lastDynamicSegmentHadGenerateStaticParams = false\n    } else if (typeof segment.generateStaticParams === 'function') {\n      lastDynamicSegmentHadGenerateStaticParams = true\n    }\n  }\n\n  // Determine if all the segments have had their parameters provided.\n  const hadAllParamsGenerated =\n    routeParamKeys.length === 0 ||\n    (routeParams.length > 0 &&\n      routeParams.every((params) => {\n        for (const key of routeParamKeys) {\n          if (key in params) continue\n          return false\n        }\n        return true\n      }))\n\n  // TODO: dynamic params should be allowed to be granular per segment but\n  // we need additional information stored/leveraged in the prerender\n  // manifest to allow this behavior.\n  const dynamicParams = segments.every(\n    (segment) => segment.config?.dynamicParams !== false\n  )\n\n  const supportsRoutePreGeneration =\n    hadAllParamsGenerated || process.env.NODE_ENV === 'production'\n\n  const fallbackMode = dynamicParams\n    ? supportsRoutePreGeneration\n      ? isRoutePPREnabled\n        ? FallbackMode.PRERENDER\n        : FallbackMode.BLOCKING_STATIC_RENDER\n      : undefined\n    : FallbackMode.NOT_FOUND\n\n  const result: Partial<StaticPathsResult> = {\n    fallbackMode,\n    prerenderedRoutes: lastDynamicSegmentHadGenerateStaticParams\n      ? []\n      : undefined,\n  }\n\n  if (hadAllParamsGenerated || isRoutePPREnabled) {\n    if (isRoutePPREnabled) {\n      // Discover all unique combinations of the rootParams so we can generate\n      // shells for each of them if they're available.\n      routeParams.unshift(\n        ...filterRootParamsCombinations(rootParamKeys, routeParams)\n      )\n\n      result.prerenderedRoutes ??= []\n      result.prerenderedRoutes.push({\n        pathname: page,\n        encodedPathname: page,\n        fallbackRouteParams: routeParamKeys,\n        fallbackMode: dynamicParams\n          ? // If the fallback params includes any root params, then we need to\n            // perform a blocking static render.\n            rootParamKeys.length > 0\n            ? FallbackMode.BLOCKING_STATIC_RENDER\n            : fallbackMode\n          : FallbackMode.NOT_FOUND,\n        fallbackRootParams: rootParamKeys,\n      })\n    }\n\n    filterUniqueParams(\n      routeParamKeys,\n      validateParams(\n        page,\n        regex,\n        isRoutePPREnabled,\n        routeParamKeys,\n        rootParamKeys,\n        routeParams\n      )\n    ).forEach((params) => {\n      let pathname: string = page\n      let encodedPathname: string = page\n\n      const fallbackRouteParams: string[] = []\n\n      for (const key of routeParamKeys) {\n        if (fallbackRouteParams.length > 0) {\n          // This is a partial route, so we should add the value to the\n          // fallbackRouteParams.\n          fallbackRouteParams.push(key)\n          continue\n        }\n\n        let paramValue = params[key]\n\n        if (!paramValue) {\n          if (isRoutePPREnabled) {\n            // This is a partial route, so we should add the value to the\n            // fallbackRouteParams.\n            fallbackRouteParams.push(key)\n            continue\n          } else {\n            // This route is not complete, and we aren't performing a partial\n            // prerender, so we should return, skipping this route.\n            return\n          }\n        }\n\n        const { repeat, optional } = regex.groups[key]\n        let replaced = `[${repeat ? '...' : ''}${key}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n\n        pathname = pathname.replace(\n          replaced,\n          encodeParam(paramValue, (value) => escapePathDelimiters(value, true))\n        )\n        encodedPathname = encodedPathname.replace(\n          replaced,\n          encodeParam(paramValue, encodeURIComponent)\n        )\n      }\n\n      const fallbackRootParams = rootParamKeys.filter((param) =>\n        fallbackRouteParams.includes(param)\n      )\n\n      result.prerenderedRoutes ??= []\n      result.prerenderedRoutes.push({\n        pathname: normalizePathname(pathname),\n        encodedPathname: normalizePathname(encodedPathname),\n        fallbackRouteParams,\n        fallbackMode: dynamicParams\n          ? // If the fallback params includes any root params, then we need to\n            // perform a blocking static render.\n            fallbackRootParams.length > 0\n            ? FallbackMode.BLOCKING_STATIC_RENDER\n            : fallbackMode\n          : FallbackMode.NOT_FOUND,\n        fallbackRootParams,\n      })\n    })\n  }\n\n  await afterRunner.executeAfter()\n\n  return result\n}\n"], "names": ["path", "After<PERSON><PERSON>ner", "createWorkStore", "FallbackMode", "getRouteMatcher", "getRouteRegex", "normalizePathname", "encodeParam", "escapePathDelimiters", "createIncrementalCache", "areParamValuesEqual", "a", "b", "Array", "isArray", "length", "every", "item", "index", "filterUniqueParams", "routeParamKeys", "routeParams", "unique", "params", "i", "j", "key", "push", "filterRootParamsCombinations", "rootParamKeys", "combinations", "combination", "validateParams", "page", "regex", "isRoutePPREnabled", "valid", "some", "Error", "join", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "undefined", "buildAppStaticPaths", "dir", "distDir", "dynamicIO", "authInterrupts", "segments", "isrFlushToDisk", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "requestHeaders", "cacheHandlers", "maxMemoryCacheSize", "fetchCacheKeyPrefix", "nextConfigOutput", "ComponentMod", "buildId", "generate", "config", "dynamicParams", "patchFetch", "incrementalCache", "flushToDisk", "cacheMaxMemorySize", "Object", "keys", "after<PERSON><PERSON>ner", "store", "fallbackRouteParams", "renderOpts", "supportsDynamicResponse", "isRevalidate", "experimental", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "previouslyRevalidatedTags", "workAsyncStorage", "run", "builtRouteParams", "parents<PERSON><PERSON><PERSON>", "idx", "current", "generateStaticParams", "fetchCache", "parentParams", "result", "lastDynamicSegmentHadGenerateStaticParams", "segment", "param", "isDynamicSegment", "relative", "filePath", "hadAllParamsGenerated", "supportsRoutePreGeneration", "process", "env", "NODE_ENV", "fallbackMode", "PRERENDER", "BLOCKING_STATIC_RENDER", "NOT_FOUND", "prerenderedRoutes", "unshift", "pathname", "encodedPathname", "fallbackRootParams", "for<PERSON>ach", "replaced", "replace", "value", "encodeURIComponent", "filter", "includes", "executeAfter"], "mappings": "AAKA,OAAOA,UAAU,OAAM;AACvB,SAASC,WAAW,QAAQ,oCAAmC;AAC/D,SAASC,eAAe,QAAQ,wCAAuC;AACvE,SAASC,YAAY,QAAQ,qBAAoB;AACjD,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SACEC,aAAa,QAER,4CAA2C;AAElD,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,UAAS;AACxD,OAAOC,0BAA0B,uDAAsD;AACvF,SAASC,sBAAsB,QAAQ,gDAA+C;AAGtF;;;;;;CAMC,GACD,SAASC,oBAAoBC,CAAa,EAAEC,CAAa;IACvD,6CAA6C;IAC7C,IAAID,MAAMC,GAAG;QACX,OAAO;IACT;IAEA,wEAAwE;IACxE,yCAAyC;IACzC,IAAIC,MAAMC,OAAO,CAACH,MAAME,MAAMC,OAAO,CAACF,IAAI;QACxC,IAAID,EAAEI,MAAM,KAAKH,EAAEG,MAAM,EAAE;YACzB,OAAO;QACT;QAEA,OAAOJ,EAAEK,KAAK,CAAC,CAACC,MAAMC,QAAUD,SAASL,CAAC,CAACM,MAAM;IACnD;IAEA,gCAAgC;IAChC,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAASC,mBACPC,cAAiC,EACjCC,WAA8B;IAE9B,MAAMC,SAAmB,EAAE;IAE3B,KAAK,MAAMC,UAAUF,YAAa;QAChC,IAAIG,IAAI;QACR,MAAOA,IAAIF,OAAOP,MAAM,EAAES,IAAK;YAC7B,MAAMP,OAAOK,MAAM,CAACE,EAAE;YACtB,IAAIC,IAAI;YACR,MAAOA,IAAIL,eAAeL,MAAM,EAAEU,IAAK;gBACrC,MAAMC,MAAMN,cAAc,CAACK,EAAE;gBAE7B,uEAAuE;gBACvE,IAAI,CAACf,oBAAoBO,IAAI,CAACS,IAAI,EAAEH,MAAM,CAACG,IAAI,GAAG;oBAChD;gBACF;YACF;YAEA,qEAAqE;YACrE,8BAA8B;YAC9B,IAAID,MAAML,eAAeL,MAAM,EAAE;gBAC/B;YACF;QACF;QAEA,yEAAyE;QACzE,8BAA8B;QAC9B,IAAIS,IAAIF,OAAOP,MAAM,EAAE;YACrB;QACF;QAEAO,OAAOK,IAAI,CAACJ;IACd;IAEA,OAAOD;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAASM,6BACPC,aAAgC,EAChCR,WAA8B;IAE9B,MAAMS,eAAyB,EAAE;IAEjC,KAAK,MAAMP,UAAUF,YAAa;QAChC,MAAMU,cAAsB,CAAC;QAE7B,yEAAyE;QACzE,IAAIP,IAAI;QACR,MAAOA,IAAIK,cAAcd,MAAM,EAAES,IAAK;YACpC,MAAME,MAAMG,aAAa,CAACL,EAAE;YAC5B,IAAID,MAAM,CAACG,IAAI,EAAE;gBACfK,WAAW,CAACL,IAAI,GAAGH,MAAM,CAACG,IAAI;YAChC,OAAO;gBACL;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QACnD,IAAIF,IAAIK,cAAcd,MAAM,EAAE;YAC5B;QACF;QAEAe,aAAaH,IAAI,CAACI;IACpB;IAEA,OAAOD;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAASE,eACPC,IAAY,EACZC,KAAiB,EACjBC,iBAA0B,EAC1Bf,cAAiC,EACjCS,aAAgC,EAChCR,WAA8B;IAE9B,MAAMe,QAAkB,EAAE;IAE1B,4EAA4E;IAC5E,qEAAqE;IACrE,IAAID,qBAAqBN,cAAcd,MAAM,GAAG,GAAG;QACjD,IACEM,YAAYN,MAAM,KAAK,KACvBc,cAAcQ,IAAI,CAAC,CAACX,MAClBL,YAAYgB,IAAI,CAAC,CAACd,SAAW,CAAEG,CAAAA,OAAOH,MAAK,KAE7C;YACA,IAAIM,cAAcd,MAAM,KAAK,GAAG;gBAC9B,MAAM,qBAEL,CAFK,IAAIuB,MACR,CAAC,2BAA2B,EAAET,aAAa,CAAC,EAAE,CAAC,+CAA+C,EAAEI,KAAK,oCAAoC,CAAC,GADtI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,sBAAsB,EAAET,cAAcU,IAAI,CAAC,MAAM,gDAAgD,EAAEN,KAAK,6CAA6C,CAAC,GADnJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,KAAK,MAAMV,UAAUF,YAAa;QAChC,MAAMJ,OAAe,CAAC;QAEtB,KAAK,MAAMS,OAAON,eAAgB;YAChC,MAAM,EAAEoB,MAAM,EAAEC,QAAQ,EAAE,GAAGP,MAAMQ,MAAM,CAAChB,IAAI;YAE9C,IAAIiB,aAAapB,MAAM,CAACG,IAAI;YAE5B,IACEe,YACAlB,OAAOqB,cAAc,CAAClB,QACrBiB,CAAAA,eAAe,QACdA,eAAeE,aACf,AAACF,eAAuB,KAAI,GAC9B;gBACAA,aAAa,EAAE;YACjB;YAEA,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,6CAA6C;YAC7C,IAAI,CAACA,cAAcR,mBAAmB;gBACpC;YACF;YAEA,sEAAsE;YACtE,oBAAoB;YACpB,IAAIK,QAAQ;gBACV,IAAI,CAAC3B,MAAMC,OAAO,CAAC6B,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAEZ,IAAI,wCAAwC,EAAE,OAAOiB,WAAW,6BAA6B,EAAEV,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF,OAAO;gBACL,IAAI,OAAOU,eAAe,UAAU;oBAClC,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAEZ,IAAI,wCAAwC,EAAE,OAAOiB,WAAW,6BAA6B,EAAEV,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEAhB,IAAI,CAACS,IAAI,GAAGiB;QACd;QAEAP,MAAMT,IAAI,CAACV;IACb;IAEA,OAAOmB;AACT;AAEA;;;;;CAKC,GACD,OAAO,eAAeU,oBAAoB,EACxCC,GAAG,EACHd,IAAI,EACJe,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZzB,oBAAoB,KAAK,EACzB0B,OAAO,EACPhC,aAAa,EAsBd;IACC,IACEsB,SAASd,IAAI,CAAC,CAACyB;YAAaA;eAAAA,EAAAA,mBAAAA,SAASC,MAAM,qBAAfD,iBAAiBE,aAAa,MAAK;UAC/DL,qBAAqB,UACrB;QACA,MAAM,qBAEL,CAFK,IAAIrB,MACR,mKADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAsB,aAAaK,UAAU;IAEvB,MAAMC,mBAAmB,MAAMzD,uBAAuB;QACpDsC;QACAC;QACAK;QACAG;QACAD;QACAG;QACAS,aAAaf;QACbgB,oBAAoBX;IACtB;IAEA,MAAMvB,QAAQ7B,cAAc4B;IAC5B,MAAMb,iBAAiBiD,OAAOC,IAAI,CAAClE,gBAAgB8B,OAAOD,SAAS,CAAC;IAEpE,MAAMsC,cAAc,IAAItE;IAExB,MAAMuE,QAAQtE,gBAAgB;QAC5B+B;QACA,sEAAsE;QACtE,QAAQ;QACRwC,qBAAqB;QACrBC,YAAY;YACVR;YACAZ;YACAqB,yBAAyB;YACzBC,cAAc;YACdC,cAAc;gBACZ5B;gBACAC;YACF;YACA4B,WAAWP,YAAYQ,OAAO,CAACD,SAAS;YACxCE,SAAST,YAAYQ,OAAO,CAACC,OAAO;YACpCC,kBAAkBV,YAAYQ,OAAO,CAACG,WAAW;QACnD;QACArB;QACAsB,2BAA2B,EAAE;IAC/B;IAEA,MAAM9D,cAAc,MAAMuC,aAAawB,gBAAgB,CAACC,GAAG,CACzDb,OACA;QACE,eAAec,iBACbC,gBAA0B,EAAE,EAC5BC,MAAM,CAAC;YAEP,yDAAyD;YACzD,IAAIA,QAAQrC,SAASpC,MAAM,EAAE,OAAOwE;YAEpC,MAAME,UAAUtC,QAAQ,CAACqC,IAAI;YAE7B,IACE,OAAOC,QAAQC,oBAAoB,KAAK,cACxCF,MAAMrC,SAASpC,MAAM,EACrB;gBACA,OAAOuE,iBAAiBC,eAAeC,MAAM;YAC/C;YAEA,MAAMjE,SAAmB,EAAE;YAE3B,IAAIkE,QAAQC,oBAAoB,EAAE;oBAIrBD;gBAHX,oEAAoE;gBACpE,0EAA0E;gBAC1E,oCAAoC;gBACpC,IAAI,SAAOA,kBAAAA,QAAQ1B,MAAM,qBAAd0B,gBAAgBE,UAAU,MAAK,aAAa;oBACrDnB,MAAMmB,UAAU,GAAGF,QAAQ1B,MAAM,CAAC4B,UAAU;gBAC9C;gBAEA,IAAIJ,cAAcxE,MAAM,GAAG,GAAG;oBAC5B,KAAK,MAAM6E,gBAAgBL,cAAe;wBACxC,MAAMM,SAAS,MAAMJ,QAAQC,oBAAoB,CAAC;4BAChDnE,QAAQqE;wBACV;wBAEA,KAAK,MAAM3E,QAAQ4E,OAAQ;4BACzBtE,OAAOI,IAAI,CAAC;gCAAE,GAAGiE,YAAY;gCAAE,GAAG3E,IAAI;4BAAC;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAM4E,SAAS,MAAMJ,QAAQC,oBAAoB,CAAC;wBAAEnE,QAAQ,CAAC;oBAAE;oBAE/DA,OAAOI,IAAI,IAAIkE;gBACjB;YACF;YAEA,IAAIL,MAAMrC,SAASpC,MAAM,EAAE;gBACzB,OAAOuE,iBAAiB/D,QAAQiE,MAAM;YACxC;YAEA,OAAOjE;QACT;QAEA,OAAO+D;IACT;IAGF,IAAIQ,4CAA4C;IAChD,KAAK,MAAMC,WAAW5C,SAAU;YAM5B4C;QALF,sEAAsE;QACtE,8BAA8B;QAC9B,IACEA,QAAQC,KAAK,IACbD,QAAQE,gBAAgB,IACxBF,EAAAA,kBAAAA,QAAQhC,MAAM,qBAAdgC,gBAAgB/B,aAAa,MAAK,OAClC;YACA,KAAK,MAAMzC,UAAUF,YAAa;gBAChC,IAAI0E,QAAQC,KAAK,IAAIzE,QAAQ;gBAE7B,MAAM2E,WAAWH,QAAQI,QAAQ,GAC7BnG,KAAKkG,QAAQ,CAACnD,KAAKgD,QAAQI,QAAQ,IACnCtD;gBAEJ,MAAM,qBAEL,CAFK,IAAIP,MACR,CAAC,SAAS,EAAE4D,SAAS,gDAAgD,EAAEH,QAAQC,KAAK,CAAC,6CAA6C,CAAC,GAD/H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IACED,QAAQE,gBAAgB,IACxB,OAAOF,QAAQL,oBAAoB,KAAK,YACxC;YACAI,4CAA4C;QAC9C,OAAO,IAAI,OAAOC,QAAQL,oBAAoB,KAAK,YAAY;YAC7DI,4CAA4C;QAC9C;IACF;IAEA,oEAAoE;IACpE,MAAMM,wBACJhF,eAAeL,MAAM,KAAK,KACzBM,YAAYN,MAAM,GAAG,KACpBM,YAAYL,KAAK,CAAC,CAACO;QACjB,KAAK,MAAMG,OAAON,eAAgB;YAChC,IAAIM,OAAOH,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEJ,wEAAwE;IACxE,mEAAmE;IACnE,mCAAmC;IACnC,MAAMyC,gBAAgBb,SAASnC,KAAK,CAClC,CAAC+E;YAAYA;eAAAA,EAAAA,kBAAAA,QAAQhC,MAAM,qBAAdgC,gBAAgB/B,aAAa,MAAK;;IAGjD,MAAMqC,6BACJD,yBAAyBE,QAAQC,GAAG,CAACC,QAAQ,KAAK;IAEpD,MAAMC,eAAezC,gBACjBqC,6BACElE,oBACEhC,aAAauG,SAAS,GACtBvG,aAAawG,sBAAsB,GACrC9D,YACF1C,aAAayG,SAAS;IAE1B,MAAMf,SAAqC;QACzCY;QACAI,mBAAmBf,4CACf,EAAE,GACFjD;IACN;IAEA,IAAIuD,yBAAyBjE,mBAAmB;QAC9C,IAAIA,mBAAmB;YACrB,wEAAwE;YACxE,gDAAgD;YAChDd,YAAYyF,OAAO,IACdlF,6BAA6BC,eAAeR;YAGjDwE,OAAOgB,iBAAiB,KAAK,EAAE;YAC/BhB,OAAOgB,iBAAiB,CAAClF,IAAI,CAAC;gBAC5BoF,UAAU9E;gBACV+E,iBAAiB/E;gBACjBwC,qBAAqBrD;gBACrBqF,cAAczC,gBAEV,oCAAoC;gBACpCnC,cAAcd,MAAM,GAAG,IACrBZ,aAAawG,sBAAsB,GACnCF,eACFtG,aAAayG,SAAS;gBAC1BK,oBAAoBpF;YACtB;QACF;QAEAV,mBACEC,gBACAY,eACEC,MACAC,OACAC,mBACAf,gBACAS,eACAR,cAEF6F,OAAO,CAAC,CAAC3F;YACT,IAAIwF,WAAmB9E;YACvB,IAAI+E,kBAA0B/E;YAE9B,MAAMwC,sBAAgC,EAAE;YAExC,KAAK,MAAM/C,OAAON,eAAgB;gBAChC,IAAIqD,oBAAoB1D,MAAM,GAAG,GAAG;oBAClC,6DAA6D;oBAC7D,uBAAuB;oBACvB0D,oBAAoB9C,IAAI,CAACD;oBACzB;gBACF;gBAEA,IAAIiB,aAAapB,MAAM,CAACG,IAAI;gBAE5B,IAAI,CAACiB,YAAY;oBACf,IAAIR,mBAAmB;wBACrB,6DAA6D;wBAC7D,uBAAuB;wBACvBsC,oBAAoB9C,IAAI,CAACD;wBACzB;oBACF,OAAO;wBACL,iEAAiE;wBACjE,uDAAuD;wBACvD;oBACF;gBACF;gBAEA,MAAM,EAAEc,MAAM,EAAEC,QAAQ,EAAE,GAAGP,MAAMQ,MAAM,CAAChB,IAAI;gBAC9C,IAAIyF,WAAW,CAAC,CAAC,EAAE3E,SAAS,QAAQ,KAAKd,IAAI,CAAC,CAAC;gBAC/C,IAAIe,UAAU;oBACZ0E,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBAEAJ,WAAWA,SAASK,OAAO,CACzBD,UACA5G,YAAYoC,YAAY,CAAC0E,QAAU7G,qBAAqB6G,OAAO;gBAEjEL,kBAAkBA,gBAAgBI,OAAO,CACvCD,UACA5G,YAAYoC,YAAY2E;YAE5B;YAEA,MAAML,qBAAqBpF,cAAc0F,MAAM,CAAC,CAACvB,QAC/CvB,oBAAoB+C,QAAQ,CAACxB;YAG/BH,OAAOgB,iBAAiB,KAAK,EAAE;YAC/BhB,OAAOgB,iBAAiB,CAAClF,IAAI,CAAC;gBAC5BoF,UAAUzG,kBAAkByG;gBAC5BC,iBAAiB1G,kBAAkB0G;gBACnCvC;gBACAgC,cAAczC,gBAEV,oCAAoC;gBACpCiD,mBAAmBlG,MAAM,GAAG,IAC1BZ,aAAawG,sBAAsB,GACnCF,eACFtG,aAAayG,SAAS;gBAC1BK;YACF;QACF;IACF;IAEA,MAAM1C,YAAYkD,YAAY;IAE9B,OAAO5B;AACT"}