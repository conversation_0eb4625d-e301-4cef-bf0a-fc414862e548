{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "sourcesContent": ["import path from 'path'\nimport { WEBPACK_LAYERS, type WebpackLayerName } from '../../lib/constants'\nimport type {\n  NextConfig,\n  ExperimentalConfig,\n  EmotionConfig,\n  StyledComponentsConfig,\n} from '../../server/config-shared'\nimport type { ResolvedBaseUrl } from '../load-jsconfig'\nimport { isWebpackServerOnlyLayer, isWebpackAppPagesLayer } from '../utils'\nimport { escapeStringRegexp } from '../../shared/lib/escape-regexp'\n\nconst nextDirname = path.dirname(require.resolve('next/package.json'))\n\nconst nextDistPath = new RegExp(\n  `${escapeStringRegexp(nextDirname)}[\\\\/]dist[\\\\/](shared[\\\\/]lib|client|pages)`\n)\n\nconst nodeModulesPath = /[\\\\/]node_modules[\\\\/]/\n\nconst regeneratorRuntimePath = require.resolve(\n  'next/dist/compiled/regenerator-runtime'\n)\n\nfunction isTypeScriptFile(filename: string) {\n  return filename.endsWith('.ts') || filename.endsWith('.tsx')\n}\n\nfunction isCommonJSFile(filename: string) {\n  return filename.endsWith('.cjs')\n}\n\n// Ensure Next.js internals and .cjs files are output as CJS modules,\n// By default all modules are output as ESM or will treated as CJS if next-swc/auto-cjs plugin detects file is CJS.\nfunction shouldOutputCommonJs(filename: string) {\n  return isCommonJSFile(filename) || nextDistPath.test(filename)\n}\n\nexport function getParserOptions({ filename, jsConfig, ...rest }: any) {\n  const isTSFile = filename.endsWith('.ts')\n  const hasTsSyntax = isTypeScriptFile(filename)\n  const enableDecorators = Boolean(\n    jsConfig?.compilerOptions?.experimentalDecorators\n  )\n  return {\n    ...rest,\n    syntax: hasTsSyntax ? 'typescript' : 'ecmascript',\n    dynamicImport: true,\n    decorators: enableDecorators,\n    // Exclude regular TypeScript files from React transformation to prevent e.g. generic parameters and angle-bracket type assertion from being interpreted as JSX tags.\n    [hasTsSyntax ? 'tsx' : 'jsx']: !isTSFile,\n    importAssertions: true,\n  }\n}\n\nfunction getBaseSWCOptions({\n  filename,\n  jest,\n  development,\n  hasReactRefresh,\n  globalWindow,\n  esm,\n  modularizeImports,\n  swcPlugins,\n  compilerOptions,\n  resolvedBaseUrl,\n  jsConfig,\n  supportedBrowsers,\n  swcCacheDir,\n  serverComponents,\n  serverReferenceHashSalt,\n  bundleLayer,\n  isDynamicIo,\n  cacheHandlers,\n  useCacheEnabled,\n}: {\n  filename: string\n  jest?: boolean\n  development: boolean\n  hasReactRefresh: boolean\n  globalWindow: boolean\n  esm: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  compilerOptions: NextConfig['compiler']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  resolvedBaseUrl?: ResolvedBaseUrl\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir?: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  isDynamicIo?: boolean\n  cacheHandlers?: ExperimentalConfig['cacheHandlers']\n  useCacheEnabled?: boolean\n}) {\n  const isReactServerLayer = isWebpackServerOnlyLayer(bundleLayer)\n  const isAppRouterPagesLayer = isWebpackAppPagesLayer(bundleLayer)\n  const parserConfig = getParserOptions({ filename, jsConfig })\n  const paths = jsConfig?.compilerOptions?.paths\n  const enableDecorators = Boolean(\n    jsConfig?.compilerOptions?.experimentalDecorators\n  )\n  const emitDecoratorMetadata = Boolean(\n    jsConfig?.compilerOptions?.emitDecoratorMetadata\n  )\n  const useDefineForClassFields = Boolean(\n    jsConfig?.compilerOptions?.useDefineForClassFields\n  )\n  const plugins = (swcPlugins ?? [])\n    .filter(Array.isArray)\n    .map(([name, options]: any) => [require.resolve(name), options])\n\n  return {\n    jsc: {\n      ...(resolvedBaseUrl && paths\n        ? {\n            baseUrl: resolvedBaseUrl.baseUrl,\n            paths,\n          }\n        : {}),\n      externalHelpers: !process.versions.pnp && !jest,\n      parser: parserConfig,\n      experimental: {\n        keepImportAttributes: true,\n        emitAssertForImportAttributes: true,\n        plugins,\n        cacheRoot: swcCacheDir,\n      },\n      transform: {\n        // Enables https://github.com/swc-project/swc/blob/0359deb4841be743d73db4536d4a22ac797d7f65/crates/swc_ecma_ext_transforms/src/jest.rs\n        ...(jest\n          ? {\n              hidden: {\n                jest: true,\n              },\n            }\n          : {}),\n        legacyDecorator: enableDecorators,\n        decoratorMetadata: emitDecoratorMetadata,\n        useDefineForClassFields: useDefineForClassFields,\n        react: {\n          importSource:\n            jsConfig?.compilerOptions?.jsxImportSource ??\n            (compilerOptions?.emotion && !isReactServerLayer\n              ? '@emotion/react'\n              : 'react'),\n          runtime: 'automatic',\n          pragmaFrag: 'React.Fragment',\n          throwIfNamespace: true,\n          development: !!development,\n          useBuiltins: true,\n          refresh: !!hasReactRefresh,\n        },\n        optimizer: {\n          simplify: false,\n          globals: jest\n            ? null\n            : {\n                typeofs: {\n                  window: globalWindow ? 'object' : 'undefined',\n                },\n                envs: {\n                  NODE_ENV: development ? '\"development\"' : '\"production\"',\n                },\n                // TODO: handle process.browser to match babel replacing as well\n              },\n        },\n        regenerator: {\n          importPath: regeneratorRuntimePath,\n        },\n      },\n    },\n    sourceMaps: jest ? 'inline' : undefined,\n    removeConsole: compilerOptions?.removeConsole,\n    // disable \"reactRemoveProperties\" when \"jest\" is true\n    // otherwise the setting from next.config.js will be used\n    reactRemoveProperties: jest\n      ? false\n      : compilerOptions?.reactRemoveProperties,\n    // Map the k-v map to an array of pairs.\n    modularizeImports: modularizeImports\n      ? Object.fromEntries(\n          Object.entries(modularizeImports).map(([mod, config]) => [\n            mod,\n            {\n              ...config,\n              transform:\n                typeof config.transform === 'string'\n                  ? config.transform\n                  : Object.entries(config.transform).map(([key, value]) => [\n                      key,\n                      value,\n                    ]),\n            },\n          ])\n        )\n      : undefined,\n    relay: compilerOptions?.relay,\n    // Always transform styled-jsx and error when `client-only` condition is triggered\n    styledJsx: compilerOptions?.styledJsx ?? {\n      useLightningcss: jsConfig?.experimental?.useLightningcss ?? false,\n    },\n    // Disable css-in-js libs (without client-only integration) transform on server layer for server components\n    ...(!isReactServerLayer && {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      emotion: getEmotionOptions(compilerOptions?.emotion, development),\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      styledComponents: getStyledComponentsOptions(\n        compilerOptions?.styledComponents,\n        development\n      ),\n    }),\n    serverComponents:\n      serverComponents && !jest\n        ? {\n            isReactServerLayer,\n            dynamicIoEnabled: isDynamicIo,\n            useCacheEnabled,\n          }\n        : undefined,\n    serverActions:\n      isAppRouterPagesLayer && !jest\n        ? {\n            isReactServerLayer,\n            isDevelopment: development,\n            useCacheEnabled,\n            hashSalt: serverReferenceHashSalt,\n            cacheKinds: ['default', 'remote'].concat(\n              cacheHandlers ? Object.keys(cacheHandlers) : []\n            ),\n          }\n        : undefined,\n    // For app router we prefer to bundle ESM,\n    // On server side of pages router we prefer CJS.\n    preferEsm: esm,\n    lintCodemodComments: true,\n    debugFunctionName: development,\n\n    ...(supportedBrowsers && supportedBrowsers.length > 0\n      ? {\n          cssEnv: {\n            targets: supportedBrowsers,\n          },\n        }\n      : {}),\n  }\n}\n\nfunction getStyledComponentsOptions(\n  styledComponentsConfig: undefined | boolean | StyledComponentsConfig,\n  development: any\n) {\n  if (!styledComponentsConfig) {\n    return null\n  } else if (typeof styledComponentsConfig === 'object') {\n    return {\n      ...styledComponentsConfig,\n      displayName: styledComponentsConfig.displayName ?? Boolean(development),\n    }\n  } else {\n    return {\n      displayName: Boolean(development),\n    }\n  }\n}\n\nfunction getEmotionOptions(\n  emotionConfig: undefined | boolean | EmotionConfig,\n  development: boolean\n) {\n  if (!emotionConfig) {\n    return null\n  }\n  let autoLabel = !!development\n  switch (typeof emotionConfig === 'object' && emotionConfig.autoLabel) {\n    case 'never':\n      autoLabel = false\n      break\n    case 'always':\n      autoLabel = true\n      break\n    case 'dev-only':\n    default:\n      break\n  }\n  return {\n    enabled: true,\n    autoLabel,\n    sourcemap: development,\n    ...(typeof emotionConfig === 'object' && {\n      importMap: emotionConfig.importMap,\n      labelFormat: emotionConfig.labelFormat,\n      sourcemap: development && emotionConfig.sourceMap,\n    }),\n  }\n}\n\nexport function getJestSWCOptions({\n  isServer,\n  filename,\n  esm,\n  modularizeImports,\n  swcPlugins,\n  compilerOptions,\n  jsConfig,\n  resolvedBaseUrl,\n  pagesDir,\n  serverReferenceHashSalt,\n}: {\n  isServer: boolean\n  filename: string\n  esm: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n  jsConfig: any\n  resolvedBaseUrl?: ResolvedBaseUrl\n  pagesDir?: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n}) {\n  let baseOptions = getBaseSWCOptions({\n    filename,\n    jest: true,\n    development: false,\n    hasReactRefresh: false,\n    globalWindow: !isServer,\n    modularizeImports,\n    swcPlugins,\n    compilerOptions,\n    jsConfig,\n    resolvedBaseUrl,\n    supportedBrowsers: undefined,\n    esm,\n    // Don't apply server layer transformations for Jest\n    // Disable server / client graph assertions for Jest\n    bundleLayer: undefined,\n    serverComponents: false,\n    serverReferenceHashSalt,\n  })\n\n  const useCjsModules = shouldOutputCommonJs(filename)\n  return {\n    ...baseOptions,\n    env: {\n      targets: {\n        // Targets the current version of Node.js\n        node: process.versions.node,\n      },\n    },\n    module: {\n      type: esm && !useCjsModules ? 'es6' : 'commonjs',\n    },\n    disableNextSsg: true,\n    disablePageConfig: true,\n    pagesDir,\n  }\n}\n\nexport function getLoaderSWCOptions({\n  // This is not passed yet as \"paths\" resolving is handled by webpack currently.\n  // resolvedBaseUrl,\n  filename,\n  development,\n  isServer,\n  pagesDir,\n  appDir,\n  isPageFile,\n  isDynamicIo,\n  hasReactRefresh,\n  modularizeImports,\n  optimizeServerReact,\n  optimizePackageImports,\n  swcPlugins,\n  compilerOptions,\n  jsConfig,\n  supportedBrowsers,\n  swcCacheDir,\n  relativeFilePathFromRoot,\n  serverComponents,\n  serverReferenceHashSalt,\n  bundleLayer,\n  esm,\n  cacheHandlers,\n  useCacheEnabled,\n}: {\n  filename: string\n  development: boolean\n  isServer: boolean\n  pagesDir?: string\n  appDir?: string\n  isPageFile: boolean\n  hasReactRefresh: boolean\n  optimizeServerReact?: boolean\n  modularizeImports: NextConfig['modularizeImports']\n  isDynamicIo?: boolean\n  optimizePackageImports?: NonNullable<\n    NextConfig['experimental']\n  >['optimizePackageImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir: string\n  relativeFilePathFromRoot: string\n  esm?: boolean\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  cacheHandlers: ExperimentalConfig['cacheHandlers']\n  useCacheEnabled?: boolean\n}) {\n  let baseOptions: any = getBaseSWCOptions({\n    filename,\n    development,\n    globalWindow: !isServer,\n    hasReactRefresh,\n    modularizeImports,\n    swcPlugins,\n    compilerOptions,\n    jsConfig,\n    // resolvedBaseUrl,\n    supportedBrowsers,\n    swcCacheDir,\n    bundleLayer,\n    serverComponents,\n    serverReferenceHashSalt,\n    esm: !!esm,\n    isDynamicIo,\n    cacheHandlers,\n    useCacheEnabled,\n  })\n  baseOptions.fontLoaders = {\n    fontLoaders: ['next/font/local', 'next/font/google'],\n    relativeFilePathFromRoot,\n  }\n  baseOptions.cjsRequireOptimizer = {\n    packages: {\n      'next/server': {\n        transforms: {\n          NextRequest: 'next/dist/server/web/spec-extension/request',\n          NextResponse: 'next/dist/server/web/spec-extension/response',\n          ImageResponse: 'next/dist/server/web/spec-extension/image-response',\n          userAgentFromString: 'next/dist/server/web/spec-extension/user-agent',\n          userAgent: 'next/dist/server/web/spec-extension/user-agent',\n        },\n      },\n    },\n  }\n\n  if (optimizeServerReact && isServer && !development) {\n    baseOptions.optimizeServerReact = {\n      optimize_use_state: false,\n    }\n  }\n\n  // Modularize import optimization for barrel files\n  if (optimizePackageImports) {\n    baseOptions.autoModularizeImports = {\n      packages: optimizePackageImports,\n    }\n  }\n\n  const isNodeModules = nodeModulesPath.test(filename)\n  const isAppBrowserLayer = bundleLayer === WEBPACK_LAYERS.appPagesBrowser\n  const moduleResolutionConfig = shouldOutputCommonJs(filename)\n    ? {\n        module: {\n          type: 'commonjs',\n        },\n      }\n    : {}\n\n  let options: any\n  if (isServer) {\n    options = {\n      ...baseOptions,\n      ...moduleResolutionConfig,\n      // Disables getStaticProps/getServerSideProps tree shaking on the server compilation for pages\n      disableNextSsg: true,\n      disablePageConfig: true,\n      isDevelopment: development,\n      isServerCompiler: isServer,\n      pagesDir,\n      appDir,\n      preferEsm: !!esm,\n      isPageFile,\n      env: {\n        targets: {\n          // Targets the current version of Node.js\n          node: process.versions.node,\n        },\n      },\n    }\n  } else {\n    options = {\n      ...baseOptions,\n      ...moduleResolutionConfig,\n      disableNextSsg: !isPageFile,\n      isDevelopment: development,\n      isServerCompiler: isServer,\n      pagesDir,\n      appDir,\n      isPageFile,\n      ...(supportedBrowsers && supportedBrowsers.length > 0\n        ? {\n            env: {\n              targets: supportedBrowsers,\n            },\n          }\n        : {}),\n    }\n    if (!options.env) {\n      // Matches default @babel/preset-env behavior\n      options.jsc.target = 'es5'\n    }\n  }\n\n  // For node_modules in app browser layer, we don't need to do any server side transformation.\n  // Only keep server actions transform to discover server actions from client components.\n  if (isAppBrowserLayer && isNodeModules) {\n    options.disableNextSsg = true\n    options.disablePageConfig = true\n    options.isPageFile = false\n    options.optimizeServerReact = undefined\n    options.cjsRequireOptimizer = undefined\n    // Disable optimizer for node_modules in app browser layer, to avoid unnecessary replacement.\n    // e.g. typeof window could result differently in js worker or browser.\n    if (\n      options.jsc.transform.optimizer.globals?.typeofs &&\n      !filename.includes(nextDirname)\n    ) {\n      delete options.jsc.transform.optimizer.globals.typeofs.window\n    }\n  }\n\n  return options\n}\n"], "names": ["path", "WEBPACK_LAYERS", "isWebpackServerOnlyLayer", "isWebpackAppPagesLayer", "escapeStringRegexp", "nextDirname", "dirname", "require", "resolve", "nextDistPath", "RegExp", "nodeModulesPath", "regeneratorRuntimePath", "isTypeScriptFile", "filename", "endsWith", "isCommonJSFile", "shouldOutputCommonJs", "test", "getParserOptions", "jsConfig", "rest", "isTSFile", "hasTsSyntax", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "esm", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "supportedBrowsers", "swcCacheDir", "serverComponents", "serverReferenceHashSalt", "bundleLayer", "isDynamicIo", "cacheHandlers", "useCacheEnabled", "isReactServerLayer", "isAppRouterPagesLayer", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "useLightningcss", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "dynamicIoEnabled", "serverActions", "isDevelopment", "hashSalt", "cacheKinds", "concat", "keys", "preferEsm", "lintCodemodComments", "debugFunctionName", "length", "cssEnv", "targets", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "enabled", "sourcemap", "importMap", "labelFormat", "sourceMap", "getJestSWCOptions", "isServer", "pagesDir", "baseOptions", "useCjsModules", "env", "node", "module", "type", "disableNextSsg", "disablePageConfig", "getLoaderSWCOptions", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isNodeModules", "isAppBrowserLayer", "appPagesBrowser", "moduleResolutionConfig", "isServerCompiler", "target", "includes"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,cAAc,QAA+B,sBAAqB;AAQ3E,SAASC,wBAAwB,EAAEC,sBAAsB,QAAQ,WAAU;AAC3E,SAASC,kBAAkB,QAAQ,iCAAgC;AAEnE,MAAMC,cAAcL,KAAKM,OAAO,CAACC,QAAQC,OAAO,CAAC;AAEjD,MAAMC,eAAe,IAAIC,OACvB,GAAGN,mBAAmBC,aAAa,2CAA2C,CAAC;AAGjF,MAAMM,kBAAkB;AAExB,MAAMC,yBAAyBL,QAAQC,OAAO,CAC5C;AAGF,SAASK,iBAAiBC,QAAgB;IACxC,OAAOA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC;AACvD;AAEA,SAASC,eAAeF,QAAgB;IACtC,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,qEAAqE;AACrE,mHAAmH;AACnH,SAASE,qBAAqBH,QAAgB;IAC5C,OAAOE,eAAeF,aAAaL,aAAaS,IAAI,CAACJ;AACvD;AAEA,OAAO,SAASK,iBAAiB,EAAEL,QAAQ,EAAEM,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWR,SAASC,QAAQ,CAAC;IACnC,MAAMQ,cAAcV,iBAAiBC;IACrC,MAAMU,mBAAmBC,QACvBL,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BO,sBAAsB;IAEnD,OAAO;QACL,GAAGN,IAAI;QACPO,QAAQL,cAAc,eAAe;QACrCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,cAAc,QAAQ,MAAM,EAAE,CAACD;QAChCS,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBlB,QAAQ,EACRmB,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfc,eAAe,EACfpB,QAAQ,EACRqB,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXC,WAAW,EACXC,aAAa,EACbC,eAAe,EAqBhB;QAIe5B,2BAEZA,4BAGAA,4BAGAA,4BAoCQA,4BA0DWA;IAzGrB,MAAM6B,qBAAqB/C,yBAAyB2C;IACpD,MAAMK,wBAAwB/C,uBAAuB0C;IACrD,MAAMM,eAAehC,iBAAiB;QAAEL;QAAUM;IAAS;IAC3D,MAAMgC,QAAQhC,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BgC,KAAK;IAC9C,MAAM5B,mBAAmBC,QACvBL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BO,sBAAsB;IAEnD,MAAM0B,wBAAwB5B,QAC5BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BiC,qBAAqB;IAElD,MAAMC,0BAA0B7B,QAC9BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BkC,uBAAuB;IAEpD,MAAMC,UAAU,AAAChB,CAAAA,cAAc,EAAE,AAAD,EAC7BiB,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAACtD,QAAQC,OAAO,CAACoD;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAItB,mBAAmBY,QACnB;gBACEW,SAASvB,gBAAgBuB,OAAO;gBAChCX;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAAClC;YAC3CmC,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAW9B;YACb;YACA+B,WAAW;gBACT,sIAAsI;gBACtI,GAAIxC,OACA;oBACEyC,QAAQ;wBACNzC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACN0C,iBAAiBnD;gBACjBoD,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACE1D,CAAAA,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B2D,eAAe,KACzCrD,CAAAA,CAAAA,mCAAAA,gBAAiBsD,OAAO,KAAI,CAAC/B,qBAC1B,mBACA,OAAM;oBACZgC,SAAS;oBACTC,YAAY;oBACZC,kBAAkB;oBAClBjD,aAAa,CAAC,CAACA;oBACfkD,aAAa;oBACbC,SAAS,CAAC,CAAClD;gBACb;gBACAmD,WAAW;oBACTC,UAAU;oBACVC,SAASvD,OACL,OACA;wBACEwD,SAAS;4BACPC,QAAQtD,eAAe,WAAW;wBACpC;wBACAuD,MAAM;4BACJC,UAAU1D,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACA2D,aAAa;oBACXC,YAAYlF;gBACd;YACF;QACF;QACAmF,YAAY9D,OAAO,WAAW+D;QAC9BC,aAAa,EAAEvE,mCAAAA,gBAAiBuE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuBjE,OACnB,QACAP,mCAAAA,gBAAiBwE,qBAAqB;QAC1C,wCAAwC;QACxC5D,mBAAmBA,oBACf6D,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAAC/D,mBAAmBqB,GAAG,CAAC,CAAC,CAAC2C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT9B,WACE,OAAO8B,OAAO9B,SAAS,KAAK,WACxB8B,OAAO9B,SAAS,GAChB0B,OAAOE,OAAO,CAACE,OAAO9B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC6C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAEhF,mCAAAA,gBAAiBgF,KAAK;QAC7B,kFAAkF;QAClFC,WAAWjF,CAAAA,mCAAAA,gBAAiBiF,SAAS,KAAI;YACvCC,iBAAiBxF,CAAAA,6BAAAA,yBAAAA,SAAUiD,YAAY,qBAAtBjD,uBAAwBwF,eAAe,KAAI;QAC9D;QACA,2GAA2G;QAC3G,GAAI,CAAC3D,sBAAsB;YACzB,mEAAmE;YACnE+B,SAAS6B,kBAAkBnF,mCAAAA,gBAAiBsD,OAAO,EAAE9C;YACrD,mEAAmE;YACnE4E,kBAAkBC,2BAChBrF,mCAAAA,gBAAiBoF,gBAAgB,EACjC5E;QAEJ,CAAC;QACDS,kBACEA,oBAAoB,CAACV,OACjB;YACEgB;YACA+D,kBAAkBlE;YAClBE;QACF,IACAgD;QACNiB,eACE/D,yBAAyB,CAACjB,OACtB;YACEgB;YACAiE,eAAehF;YACfc;YACAmE,UAAUvE;YACVwE,YAAY;gBAAC;gBAAW;aAAS,CAACC,MAAM,CACtCtE,gBAAgBoD,OAAOmB,IAAI,CAACvE,iBAAiB,EAAE;QAEnD,IACAiD;QACN,0CAA0C;QAC1C,gDAAgD;QAChDuB,WAAWlF;QACXmF,qBAAqB;QACrBC,mBAAmBvF;QAEnB,GAAIO,qBAAqBA,kBAAkBiF,MAAM,GAAG,IAChD;YACEC,QAAQ;gBACNC,SAASnF;YACX;QACF,IACA,CAAC,CAAC;IACR;AACF;AAEA,SAASsE,2BACPc,sBAAoE,EACpE3F,WAAgB;IAEhB,IAAI,CAAC2F,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAIrG,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACL4F,aAAarG,QAAQS;QACvB;IACF;AACF;AAEA,SAAS2E,kBACPkB,aAAkD,EAClD7F,WAAoB;IAEpB,IAAI,CAAC6F,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAAC9F;IAClB,OAAQ,OAAO6F,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLC,SAAS;QACTD;QACAE,WAAWhG;QACX,GAAI,OAAO6F,kBAAkB,YAAY;YACvCI,WAAWJ,cAAcI,SAAS;YAClCC,aAAaL,cAAcK,WAAW;YACtCF,WAAWhG,eAAe6F,cAAcM,SAAS;QACnD,CAAC;IACH;AACF;AAEA,OAAO,SAASC,kBAAkB,EAChCC,QAAQ,EACRzH,QAAQ,EACRuB,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRoB,eAAe,EACfgG,QAAQ,EACR5F,uBAAuB,EAaxB;IACC,IAAI6F,cAAczG,kBAAkB;QAClClB;QACAmB,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAACmG;QACfjG;QACAC;QACAb;QACAN;QACAoB;QACAC,mBAAmBuD;QACnB3D;QACA,oDAAoD;QACpD,oDAAoD;QACpDQ,aAAamD;QACbrD,kBAAkB;QAClBC;IACF;IAEA,MAAM8F,gBAAgBzH,qBAAqBH;IAC3C,OAAO;QACL,GAAG2H,WAAW;QACdE,KAAK;YACHf,SAAS;gBACP,yCAAyC;gBACzCgB,MAAM3E,QAAQC,QAAQ,CAAC0E,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAMzG,OAAO,CAACqG,gBAAgB,QAAQ;QACxC;QACAK,gBAAgB;QAChBC,mBAAmB;QACnBR;IACF;AACF;AAEA,OAAO,SAASS,oBAAoB,EAClC,+EAA+E;AAC/E,mBAAmB;AACnBnI,QAAQ,EACRoB,WAAW,EACXqG,QAAQ,EACRC,QAAQ,EACRU,MAAM,EACNC,UAAU,EACVrG,WAAW,EACXX,eAAe,EACfG,iBAAiB,EACjB8G,mBAAmB,EACnBC,sBAAsB,EACtB9G,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRqB,iBAAiB,EACjBC,WAAW,EACX4G,wBAAwB,EACxB3G,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXR,GAAG,EACHU,aAAa,EACbC,eAAe,EA2BhB;IACC,IAAIyF,cAAmBzG,kBAAkB;QACvClB;QACAoB;QACAE,cAAc,CAACmG;QACfpG;QACAG;QACAC;QACAb;QACAN;QACA,mBAAmB;QACnBqB;QACAC;QACAG;QACAF;QACAC;QACAP,KAAK,CAAC,CAACA;QACPS;QACAC;QACAC;IACF;IACAyF,YAAYc,WAAW,GAAG;QACxBA,aAAa;YAAC;YAAmB;SAAmB;QACpDD;IACF;IACAb,YAAYe,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIX,uBAAuBb,YAAY,CAACrG,aAAa;QACnDuG,YAAYW,mBAAmB,GAAG;YAChCY,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIX,wBAAwB;QAC1BZ,YAAYwB,qBAAqB,GAAG;YAClCR,UAAUJ;QACZ;IACF;IAEA,MAAMa,gBAAgBvJ,gBAAgBO,IAAI,CAACJ;IAC3C,MAAMqJ,oBAAoBtH,gBAAgB5C,eAAemK,eAAe;IACxE,MAAMC,yBAAyBpJ,qBAAqBH,YAChD;QACE+H,QAAQ;YACNC,MAAM;QACR;IACF,IACA,CAAC;IAEL,IAAIjF;IACJ,IAAI0E,UAAU;QACZ1E,UAAU;YACR,GAAG4E,WAAW;YACd,GAAG4B,sBAAsB;YACzB,8FAA8F;YAC9FtB,gBAAgB;YAChBC,mBAAmB;YACnB9B,eAAehF;YACfoI,kBAAkB/B;YAClBC;YACAU;YACA3B,WAAW,CAAC,CAAClF;YACb8G;YACAR,KAAK;gBACHf,SAAS;oBACP,yCAAyC;oBACzCgB,MAAM3E,QAAQC,QAAQ,CAAC0E,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACL/E,UAAU;YACR,GAAG4E,WAAW;YACd,GAAG4B,sBAAsB;YACzBtB,gBAAgB,CAACI;YACjBjC,eAAehF;YACfoI,kBAAkB/B;YAClBC;YACAU;YACAC;YACA,GAAI1G,qBAAqBA,kBAAkBiF,MAAM,GAAG,IAChD;gBACEiB,KAAK;oBACHf,SAASnF;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAACoB,QAAQ8E,GAAG,EAAE;YAChB,6CAA6C;YAC7C9E,QAAQC,GAAG,CAACyG,MAAM,GAAG;QACvB;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAIJ,qBAAqBD,eAAe;YASpCrG;QARFA,QAAQkF,cAAc,GAAG;QACzBlF,QAAQmF,iBAAiB,GAAG;QAC5BnF,QAAQsF,UAAU,GAAG;QACrBtF,QAAQuF,mBAAmB,GAAGpD;QAC9BnC,QAAQ2F,mBAAmB,GAAGxD;QAC9B,6FAA6F;QAC7F,uEAAuE;QACvE,IACEnC,EAAAA,2CAAAA,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,qBAAvC3B,yCAAyC4B,OAAO,KAChD,CAAC3E,SAAS0J,QAAQ,CAACnK,cACnB;YACA,OAAOwD,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,CAACC,OAAO,CAACC,MAAM;QAC/D;IACF;IAEA,OAAO7B;AACT"}