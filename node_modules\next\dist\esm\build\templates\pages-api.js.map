{"version": 3, "sources": ["../../../src/build/templates/pages-api.ts"], "sourcesContent": ["import { PagesAPIRouteModule } from '../../server/route-modules/pages-api/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\n\nimport { hoist } from './helpers'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\n\n// Re-export the handler (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export config.\nexport const config = hoist(userland, 'config')\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesAPIRouteModule({\n  definition: {\n    kind: RouteKind.PAGES_API,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  userland,\n})\n"], "names": ["PagesAPIRouteModule", "RouteKind", "hoist", "userland", "config", "routeModule", "definition", "kind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,SAAS,QAAQ,0BAAyB;AAEnD,SAASC,KAAK,QAAQ,YAAW;AAEjC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;AAExC,wDAAwD;AACxD,eAAeD,MAAMC,UAAU,WAAU;AAEzC,oBAAoB;AACpB,OAAO,MAAMC,SAASF,MAAMC,UAAU,UAAS;AAE/C,4DAA4D;AAC5D,OAAO,MAAME,cAAc,IAAIL,oBAAoB;IACjDM,YAAY;QACVC,MAAMN,UAAUO,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAT;AACF,GAAE"}