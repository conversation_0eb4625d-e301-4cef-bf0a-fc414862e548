{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/index.ts"], "sourcesContent": ["export type { SerializableTurborepoAccessTraceResult } from './types'\nexport {\n  turborepoTraceAccess,\n  writeTurborepoAccessTraceResult,\n} from './helpers'\nexport { TurborepoAccessTraceResult } from './result'\n"], "names": ["turborepoTraceAccess", "writeTurborepoAccessTraceResult", "TurborepoAccessTraceResult"], "mappings": "AACA,SACEA,oBAAoB,EACpBC,+BAA+B,QAC1B,YAAW;AAClB,SAASC,0BAA0B,QAAQ,WAAU"}