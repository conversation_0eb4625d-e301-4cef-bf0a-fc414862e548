{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/index.ts"], "sourcesContent": ["import curry from 'next/dist/compiled/lodash.curry'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { loader, plugin } from '../../helpers'\nimport { pipe } from '../../utils'\nimport type { ConfigurationContext, ConfigurationFn } from '../../utils'\nimport { getCssModuleLoader, getGlobalCssLoader } from './loaders'\nimport { getNextFontLoader } from './loaders/next-font'\nimport {\n  getCustomDocumentError,\n  getGlobalImportError,\n  getGlobalModuleImportError,\n  getLocalModuleImportError,\n} from './messages'\nimport { getPostCssPlugins } from './plugins'\nimport { nonNullable } from '../../../../../lib/non-nullable'\nimport { WEBPACK_LAYERS } from '../../../../../lib/constants'\nimport { getRspackCore } from '../../../../../shared/lib/get-rspack'\n\n// RegExps for all Style Sheet variants\nexport const regexLikeCss = /\\.(css|scss|sass)$/\n\n// RegExps for Style Sheets\nconst regexCssGlobal = /(?<!\\.module)\\.css$/\nconst regexCssModules = /\\.module\\.css$/\n\n// RegExps for Syntactically Awesome Style Sheets\nconst regexSassGlobal = /(?<!\\.module)\\.(scss|sass)$/\nconst regexSassModules = /\\.module\\.(scss|sass)$/\n\nconst APP_LAYER_RULE = {\n  or: [\n    WEBPACK_LAYERS.reactServerComponents,\n    WEBPACK_LAYERS.serverSideRendering,\n    WEBPACK_LAYERS.appPagesBrowser,\n  ],\n}\n\nconst PAGES_LAYER_RULE = {\n  not: [\n    WEBPACK_LAYERS.reactServerComponents,\n    WEBPACK_LAYERS.serverSideRendering,\n    WEBPACK_LAYERS.appPagesBrowser,\n  ],\n}\n\n/**\n * Mark a rule as removable if built-in CSS support is disabled\n */\nfunction markRemovable(r: webpack.RuleSetRule): webpack.RuleSetRule {\n  Object.defineProperty(r, Symbol.for('__next_css_remove'), {\n    enumerable: false,\n    value: true,\n  })\n  return r\n}\n\nlet postcssInstancePromise: Promise<any>\nexport async function lazyPostCSS(\n  rootDirectory: string,\n  supportedBrowsers: string[] | undefined,\n  disablePostcssPresetEnv: boolean | undefined,\n  useLightningcss: boolean | undefined\n) {\n  if (!postcssInstancePromise) {\n    postcssInstancePromise = (async () => {\n      const postcss = require('postcss')\n      // @ts-ignore backwards compat\n      postcss.plugin = function postcssPlugin(name, initializer) {\n        function creator(...args: any) {\n          let transformer = initializer(...args)\n          transformer.postcssPlugin = name\n          // transformer.postcssVersion = new Processor().version\n          return transformer\n        }\n\n        let cache: any\n        Object.defineProperty(creator, 'postcss', {\n          get() {\n            if (!cache) cache = creator()\n            return cache\n          },\n        })\n\n        creator.process = function (\n          css: any,\n          processOpts: any,\n          pluginOpts: any\n        ) {\n          return postcss([creator(pluginOpts)]).process(css, processOpts)\n        }\n\n        return creator\n      }\n\n      // @ts-ignore backwards compat\n      postcss.vendor = {\n        /**\n         * Returns the vendor prefix extracted from an input string.\n         *\n         * @example\n         * postcss.vendor.prefix('-moz-tab-size') //=> '-moz-'\n         * postcss.vendor.prefix('tab-size')      //=> ''\n         */\n        prefix: function prefix(prop: string): string {\n          const match = prop.match(/^(-\\w+-)/)\n\n          if (match) {\n            return match[0]\n          }\n\n          return ''\n        },\n\n        /**\n         * Returns the input string stripped of its vendor prefix.\n         *\n         * @example\n         * postcss.vendor.unprefixed('-moz-tab-size') //=> 'tab-size'\n         */\n        unprefixed: function unprefixed(\n          /**\n           * String with or without vendor prefix.\n           */\n          prop: string\n        ): string {\n          return prop.replace(/^-\\w+-/, '')\n        },\n      }\n\n      const postCssPlugins = await getPostCssPlugins(\n        rootDirectory,\n        supportedBrowsers,\n        disablePostcssPresetEnv,\n        useLightningcss\n      )\n\n      return {\n        postcss,\n        postcssWithPlugins: postcss(postCssPlugins),\n      }\n    })()\n  }\n\n  return postcssInstancePromise\n}\n\nexport const css = curry(async function css(\n  ctx: ConfigurationContext,\n  config: webpack.Configuration\n) {\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n  const {\n    prependData: sassPrependData,\n    additionalData: sassAdditionalData,\n    implementation: sassImplementation,\n    ...sassOptions\n  } = ctx.sassOptions\n\n  const lazyPostCSSInitializer = () =>\n    lazyPostCSS(\n      ctx.rootDirectory,\n      ctx.supportedBrowsers,\n      ctx.experimental.disablePostcssPresetEnv,\n      ctx.experimental.useLightningcss\n    )\n\n  const sassPreprocessors: webpack.RuleSetUseItem[] = [\n    // First, process files with `sass-loader`: this inlines content, and\n    // compiles away the proprietary syntax.\n    {\n      loader: require.resolve('next/dist/compiled/sass-loader'),\n      options: {\n        implementation: sassImplementation,\n        // Source maps are required so that `resolve-url-loader` can locate\n        // files original to their source directory.\n        sourceMap: true,\n        sassOptions: {\n          // The \"fibers\" option is not needed for Node.js 16+, but it's causing\n          // problems for Node.js <= 14 users as you'll have to manually install\n          // the `fibers` package:\n          // https://github.com/webpack-contrib/sass-loader#:~:text=We%20automatically%20inject%20the%20fibers%20package\n          // https://github.com/vercel/next.js/issues/45052\n          // Since it's optional and not required, we'll disable it by default\n          // to avoid the confusion.\n          fibers: false,\n          // TODO: Remove this once we upgrade to sass-loader 16\n          silenceDeprecations: ['legacy-js-api'],\n          ...sassOptions,\n        },\n        additionalData: sassPrependData || sassAdditionalData,\n      },\n    },\n    // Then, `sass-loader` will have passed-through CSS imports as-is instead\n    // of inlining them. Because they were inlined, the paths are no longer\n    // correct.\n    // To fix this, we use `resolve-url-loader` to rewrite the CSS\n    // imports to real file paths.\n    {\n      loader: require.resolve('../../../loaders/resolve-url-loader/index'),\n      options: {\n        postcss: lazyPostCSSInitializer,\n        // Source maps are not required here, but we may as well emit\n        // them.\n        sourceMap: true,\n      },\n    },\n  ]\n\n  const fns: ConfigurationFn[] = []\n\n  const googleLoader = require.resolve(\n    'next/dist/compiled/@next/font/google/loader'\n  )\n  const localLoader = require.resolve(\n    'next/dist/compiled/@next/font/local/loader'\n  )\n  const nextFontLoaders: Array<[string | RegExp, string, any?]> = [\n    [require.resolve('next/font/google/target.css'), googleLoader],\n    [require.resolve('next/font/local/target.css'), localLoader],\n  ]\n\n  nextFontLoaders.forEach(([fontLoaderTarget, fontLoaderPath]) => {\n    // Matches the resolved font loaders noop files to run next-font-loader\n    fns.push(\n      loader({\n        oneOf: [\n          markRemovable({\n            sideEffects: false,\n            test: fontLoaderTarget,\n            use: getNextFontLoader(ctx, lazyPostCSSInitializer, fontLoaderPath),\n          }),\n        ],\n      })\n    )\n  })\n\n  // CSS cannot be imported in _document. This comes before everything because\n  // global CSS nor CSS modules work in said file.\n  fns.push(\n    loader({\n      oneOf: [\n        markRemovable({\n          test: regexLikeCss,\n          // Use a loose regex so we don't have to crawl the file system to\n          // find the real file name (if present).\n          issuer: /pages[\\\\/]_document\\./,\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getCustomDocumentError(),\n            },\n          },\n        }),\n      ],\n    })\n  )\n\n  const shouldIncludeExternalCSSImports =\n    !!ctx.experimental.craCompat || !!ctx.transpilePackages\n\n  // CSS modules & SASS modules support. They are allowed to be imported in anywhere.\n  fns.push(\n    // CSS Modules should never have side effects. This setting will\n    // allow unused CSS to be removed from the production build.\n    // We ensure this by disallowing `:global()` CSS at the top-level\n    // via the `pure` mode in `css-loader`.\n    loader({\n      oneOf: [\n        // For app dir, we need to match the specific app layer.\n        ctx.hasAppDir\n          ? markRemovable({\n              sideEffects: true,\n              test: regexCssModules,\n              issuerLayer: APP_LAYER_RULE,\n              use: [\n                {\n                  loader: require.resolve(\n                    '../../../loaders/next-flight-css-loader'\n                  ),\n                  options: {\n                    cssModules: true,\n                  },\n                },\n                ...getCssModuleLoader(\n                  { ...ctx, isAppDir: true },\n                  lazyPostCSSInitializer\n                ),\n              ],\n            })\n          : null,\n        markRemovable({\n          sideEffects: true,\n          test: regexCssModules,\n          issuerLayer: PAGES_LAYER_RULE,\n          use: getCssModuleLoader(\n            { ...ctx, isAppDir: false },\n            lazyPostCSSInitializer\n          ),\n        }),\n      ].filter(nonNullable),\n    }),\n    // Opt-in support for Sass (using .scss or .sass extensions).\n    // Sass Modules should never have side effects. This setting will\n    // allow unused Sass to be removed from the production build.\n    // We ensure this by disallowing `:global()` Sass at the top-level\n    // via the `pure` mode in `css-loader`.\n    loader({\n      oneOf: [\n        // For app dir, we need to match the specific app layer.\n        ctx.hasAppDir\n          ? markRemovable({\n              sideEffects: true,\n              test: regexSassModules,\n              issuerLayer: APP_LAYER_RULE,\n              use: [\n                {\n                  loader: require.resolve(\n                    '../../../loaders/next-flight-css-loader'\n                  ),\n                  options: {\n                    cssModules: true,\n                  },\n                },\n                ...getCssModuleLoader(\n                  { ...ctx, isAppDir: true },\n                  lazyPostCSSInitializer,\n                  sassPreprocessors\n                ),\n              ],\n            })\n          : null,\n        markRemovable({\n          sideEffects: true,\n          test: regexSassModules,\n          issuerLayer: PAGES_LAYER_RULE,\n          use: getCssModuleLoader(\n            { ...ctx, isAppDir: false },\n            lazyPostCSSInitializer,\n            sassPreprocessors\n          ),\n        }),\n      ].filter(nonNullable),\n    }),\n    // Throw an error for CSS Modules used outside their supported scope\n    loader({\n      oneOf: [\n        markRemovable({\n          test: [regexCssModules, regexSassModules],\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getLocalModuleImportError(),\n            },\n          },\n        }),\n      ],\n    })\n  )\n\n  // Global CSS and SASS support.\n  if (ctx.isServer) {\n    fns.push(\n      loader({\n        oneOf: [\n          ctx.hasAppDir && !ctx.isProduction\n            ? markRemovable({\n                sideEffects: true,\n                test: [regexCssGlobal, regexSassGlobal],\n                issuerLayer: APP_LAYER_RULE,\n                use: {\n                  loader: require.resolve(\n                    '../../../loaders/next-flight-css-loader'\n                  ),\n                  options: {\n                    cssModules: false,\n                  },\n                },\n              })\n            : null,\n          markRemovable({\n            // CSS imports have side effects, even on the server side.\n            sideEffects: true,\n            test: [regexCssGlobal, regexSassGlobal],\n            use: require.resolve('next/dist/compiled/ignore-loader'),\n          }),\n        ].filter(nonNullable),\n      })\n    )\n  } else {\n    // External CSS files are allowed to be loaded when any of the following is true:\n    // - hasAppDir: all CSS files are allowed\n    // - If the CSS file is located in `node_modules`\n    // - If the CSS file is located in another package in a monorepo (outside of the current rootDir)\n    // - If the issuer is pages/_app (matched later)\n    const allowedPagesGlobalCSSPath = ctx.hasAppDir\n      ? undefined\n      : {\n          and: [\n            {\n              or: [\n                /node_modules/,\n                {\n                  not: [ctx.rootDirectory],\n                },\n              ],\n            },\n          ],\n        }\n    const allowedPagesGlobalCSSIssuer = ctx.hasAppDir\n      ? undefined\n      : shouldIncludeExternalCSSImports\n        ? undefined\n        : {\n            and: [ctx.rootDirectory],\n            not: [/node_modules/],\n          }\n\n    fns.push(\n      loader({\n        oneOf: [\n          ...(ctx.hasAppDir\n            ? [\n                markRemovable({\n                  sideEffects: true,\n                  test: regexCssGlobal,\n                  issuerLayer: APP_LAYER_RULE,\n                  use: [\n                    {\n                      loader: require.resolve(\n                        '../../../loaders/next-flight-css-loader'\n                      ),\n                      options: {\n                        cssModules: false,\n                      },\n                    },\n                    ...getGlobalCssLoader(\n                      { ...ctx, isAppDir: true },\n                      lazyPostCSSInitializer\n                    ),\n                  ],\n                }),\n                markRemovable({\n                  sideEffects: true,\n                  test: regexSassGlobal,\n                  issuerLayer: APP_LAYER_RULE,\n                  use: [\n                    {\n                      loader: require.resolve(\n                        '../../../loaders/next-flight-css-loader'\n                      ),\n                      options: {\n                        cssModules: false,\n                      },\n                    },\n                    ...getGlobalCssLoader(\n                      { ...ctx, isAppDir: true },\n                      lazyPostCSSInitializer,\n                      sassPreprocessors\n                    ),\n                  ],\n                }),\n              ]\n            : []),\n          markRemovable({\n            sideEffects: true,\n            test: regexCssGlobal,\n            include: allowedPagesGlobalCSSPath,\n            issuer: allowedPagesGlobalCSSIssuer,\n            issuerLayer: PAGES_LAYER_RULE,\n            use: getGlobalCssLoader(\n              { ...ctx, isAppDir: false },\n              lazyPostCSSInitializer\n            ),\n          }),\n          markRemovable({\n            sideEffects: true,\n            test: regexSassGlobal,\n            include: allowedPagesGlobalCSSPath,\n            issuer: allowedPagesGlobalCSSIssuer,\n            issuerLayer: PAGES_LAYER_RULE,\n            use: getGlobalCssLoader(\n              { ...ctx, isAppDir: false },\n              lazyPostCSSInitializer,\n              sassPreprocessors\n            ),\n          }),\n        ].filter(nonNullable),\n      })\n    )\n\n    if (ctx.customAppFile) {\n      fns.push(\n        loader({\n          oneOf: [\n            markRemovable({\n              sideEffects: true,\n              test: regexCssGlobal,\n              issuer: { and: [ctx.customAppFile] },\n              use: getGlobalCssLoader(\n                { ...ctx, isAppDir: false },\n                lazyPostCSSInitializer\n              ),\n            }),\n          ],\n        }),\n        loader({\n          oneOf: [\n            markRemovable({\n              sideEffects: true,\n              test: regexSassGlobal,\n              issuer: { and: [ctx.customAppFile] },\n              use: getGlobalCssLoader(\n                { ...ctx, isAppDir: false },\n                lazyPostCSSInitializer,\n                sassPreprocessors\n              ),\n            }),\n          ],\n        })\n      )\n    }\n  }\n\n  // Throw an error for Global CSS used inside of `node_modules`\n  if (!shouldIncludeExternalCSSImports) {\n    fns.push(\n      loader({\n        oneOf: [\n          markRemovable({\n            test: [regexCssGlobal, regexSassGlobal],\n            issuer: { and: [/node_modules/] },\n            use: {\n              loader: 'error-loader',\n              options: {\n                reason: getGlobalModuleImportError(),\n              },\n            },\n          }),\n        ],\n      })\n    )\n  }\n\n  // Throw an error for Global CSS used outside of our custom <App> file\n  fns.push(\n    loader({\n      oneOf: [\n        markRemovable({\n          test: [regexCssGlobal, regexSassGlobal],\n          issuer: ctx.hasAppDir\n            ? {\n                // If it's inside the app dir, but not importing from a layout file,\n                // throw an error.\n                and: [ctx.rootDirectory],\n                not: [/layout\\.(js|mjs|jsx|ts|tsx)$/],\n              }\n            : undefined,\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getGlobalImportError(),\n            },\n          },\n        }),\n      ],\n    })\n  )\n\n  if (ctx.isClient) {\n    // Automatically transform references to files (i.e. url()) into URLs\n    // e.g. url(./logo.svg)\n    fns.push(\n      loader({\n        oneOf: [\n          markRemovable({\n            // This should only be applied to CSS files\n            issuer: regexLikeCss,\n            // Exclude extensions that webpack handles by default\n            exclude: [\n              /\\.(js|mjs|jsx|ts|tsx)$/,\n              /\\.html$/,\n              /\\.json$/,\n              /\\.webpack\\[[^\\]]+\\]$/,\n            ],\n            // `asset/resource` always emits a URL reference, where `asset`\n            // might inline the asset as a data URI\n            type: 'asset/resource',\n          }),\n        ],\n      })\n    )\n  }\n\n  // Enable full mini-css-extract-plugin hmr for prod mode pages or app dir\n  if (ctx.isClient && (ctx.isProduction || ctx.hasAppDir)) {\n    // Extract CSS as CSS file(s) in the client-side production bundle.\n    const MiniCssExtractPlugin = isRspack\n      ? getRspackCore().CssExtractRspackPlugin\n      : require('../../../plugins/mini-css-extract-plugin').default\n\n    fns.push(\n      plugin(\n        // @ts-ignore webpack 5 compat\n        new MiniCssExtractPlugin({\n          filename: ctx.isProduction\n            ? 'static/css/[contenthash].css'\n            : 'static/css/[name].css',\n          chunkFilename: ctx.isProduction\n            ? 'static/css/[contenthash].css'\n            : 'static/css/[name].css',\n          // Next.js guarantees that CSS order \"doesn't matter\", due to imposed\n          // restrictions:\n          // 1. Global CSS can only be defined in a single entrypoint (_app)\n          // 2. CSS Modules generate scoped class names by default and cannot\n          //    include Global CSS (:global() selector).\n          //\n          // While not a perfect guarantee (e.g. liberal use of `:global()`\n          // selector), this assumption is required to code-split CSS.\n          //\n          // If this warning were to trigger, it'd be unactionable by the user,\n          // but likely not valid -- so we disable it.\n          ignoreOrder: true,\n          insert: function (linkTag: HTMLLinkElement) {\n            if (typeof _N_E_STYLE_LOAD === 'function') {\n              const { href, onload, onerror } = linkTag\n              _N_E_STYLE_LOAD(\n                href.indexOf(window.location.origin) === 0\n                  ? new URL(href).pathname\n                  : href\n              ).then(\n                () => onload?.call(linkTag, { type: 'load' } as Event),\n                () => onerror?.call(linkTag, {} as Event)\n              )\n            } else {\n              document.head.appendChild(linkTag)\n            }\n          },\n        })\n      )\n    )\n  }\n\n  const fn = pipe(...fns)\n  return fn(config)\n})\n"], "names": ["curry", "loader", "plugin", "pipe", "getCssModuleLoader", "getGlobalCssLoader", "getNextFontLoader", "getCustomDocumentError", "getGlobalImportError", "getGlobalModuleImportError", "getLocalModuleImportError", "getPostCssPlugins", "nonNullable", "WEBPACK_LAYERS", "getRspackCore", "regexLikeCss", "regexCssGlobal", "regexCssModules", "regexSassGlobal", "regexSassModules", "APP_LAYER_RULE", "or", "reactServerComponents", "serverSideRendering", "appPagesBrowser", "PAGES_LAYER_RULE", "not", "markRemovable", "r", "Object", "defineProperty", "Symbol", "for", "enumerable", "value", "postcssInstancePromise", "lazyPostCSS", "rootDirectory", "supportedBrowsers", "disablePostcssPresetEnv", "useLightningcss", "postcss", "require", "postcssPlugin", "name", "initializer", "creator", "args", "transformer", "cache", "get", "process", "css", "processOpts", "pluginOpts", "vendor", "prefix", "prop", "match", "unprefixed", "replace", "post<PERSON>s<PERSON><PERSON><PERSON>", "postcssWithPlugins", "ctx", "config", "isRspack", "Boolean", "env", "NEXT_RSPACK", "prependData", "sassPrependData", "additionalData", "sassAdditionalData", "implementation", "sassImplementation", "sassOptions", "lazyPostCSSInitializer", "experimental", "sassPreprocessors", "resolve", "options", "sourceMap", "fibers", "silenceDeprecations", "fns", "google<PERSON><PERSON>der", "localLoader", "nextFontLoaders", "for<PERSON>ach", "fontLoaderTarget", "fontLoaderPath", "push", "oneOf", "sideEffects", "test", "use", "issuer", "reason", "shouldIncludeExternalCSSImports", "craCompat", "transpilePackages", "hasAppDir", "issuer<PERSON><PERSON>er", "cssModules", "isAppDir", "filter", "isServer", "isProduction", "allowedPagesGlobalCSSPath", "undefined", "and", "allowedPagesGlobalCSSIssuer", "include", "customAppFile", "isClient", "exclude", "type", "MiniCssExtractPlugin", "CssExtractRspackPlugin", "default", "filename", "chunkFilename", "ignoreOrder", "insert", "linkTag", "_N_E_STYLE_LOAD", "href", "onload", "onerror", "indexOf", "window", "location", "origin", "URL", "pathname", "then", "call", "document", "head", "append<PERSON><PERSON><PERSON>", "fn"], "mappings": "AAAA,OAAOA,WAAW,kCAAiC;AAEnD,SAASC,MAAM,EAAEC,MAAM,QAAQ,gBAAe;AAC9C,SAASC,IAAI,QAAQ,cAAa;AAElC,SAASC,kBAAkB,EAAEC,kBAAkB,QAAQ,YAAW;AAClE,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,yBAAyB,QACpB,aAAY;AACnB,SAASC,iBAAiB,QAAQ,YAAW;AAC7C,SAASC,WAAW,QAAQ,kCAAiC;AAC7D,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,aAAa,QAAQ,uCAAsC;AAEpE,uCAAuC;AACvC,OAAO,MAAMC,eAAe,qBAAoB;AAEhD,2BAA2B;AAC3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB;AAExB,iDAAiD;AACjD,MAAMC,kBAAkB;AACxB,MAAMC,mBAAmB;AAEzB,MAAMC,iBAAiB;IACrBC,IAAI;QACFR,eAAeS,qBAAqB;QACpCT,eAAeU,mBAAmB;QAClCV,eAAeW,eAAe;KAC/B;AACH;AAEA,MAAMC,mBAAmB;IACvBC,KAAK;QACHb,eAAeS,qBAAqB;QACpCT,eAAeU,mBAAmB;QAClCV,eAAeW,eAAe;KAC/B;AACH;AAEA;;CAEC,GACD,SAASG,cAAcC,CAAsB;IAC3CC,OAAOC,cAAc,CAACF,GAAGG,OAAOC,GAAG,CAAC,sBAAsB;QACxDC,YAAY;QACZC,OAAO;IACT;IACA,OAAON;AACT;AAEA,IAAIO;AACJ,OAAO,eAAeC,YACpBC,aAAqB,EACrBC,iBAAuC,EACvCC,uBAA4C,EAC5CC,eAAoC;IAEpC,IAAI,CAACL,wBAAwB;QAC3BA,yBAAyB,AAAC,CAAA;YACxB,MAAMM,UAAUC,QAAQ;YACxB,8BAA8B;YAC9BD,QAAQvC,MAAM,GAAG,SAASyC,cAAcC,IAAI,EAAEC,WAAW;gBACvD,SAASC,QAAQ,GAAGC,IAAS;oBAC3B,IAAIC,cAAcH,eAAeE;oBACjCC,YAAYL,aAAa,GAAGC;oBAC5B,uDAAuD;oBACvD,OAAOI;gBACT;gBAEA,IAAIC;gBACJpB,OAAOC,cAAc,CAACgB,SAAS,WAAW;oBACxCI;wBACE,IAAI,CAACD,OAAOA,QAAQH;wBACpB,OAAOG;oBACT;gBACF;gBAEAH,QAAQK,OAAO,GAAG,SAChBC,GAAQ,EACRC,WAAgB,EAChBC,UAAe;oBAEf,OAAOb,QAAQ;wBAACK,QAAQQ;qBAAY,EAAEH,OAAO,CAACC,KAAKC;gBACrD;gBAEA,OAAOP;YACT;YAEA,8BAA8B;YAC9BL,QAAQc,MAAM,GAAG;gBACf;;;;;;SAMC,GACDC,QAAQ,SAASA,OAAOC,IAAY;oBAClC,MAAMC,QAAQD,KAAKC,KAAK,CAAC;oBAEzB,IAAIA,OAAO;wBACT,OAAOA,KAAK,CAAC,EAAE;oBACjB;oBAEA,OAAO;gBACT;gBAEA;;;;;SAKC,GACDC,YAAY,SAASA,WACnB;;WAEC,GACDF,IAAY;oBAEZ,OAAOA,KAAKG,OAAO,CAAC,UAAU;gBAChC;YACF;YAEA,MAAMC,iBAAiB,MAAMlD,kBAC3B0B,eACAC,mBACAC,yBACAC;YAGF,OAAO;gBACLC;gBACAqB,oBAAoBrB,QAAQoB;YAC9B;QACF,CAAA;IACF;IAEA,OAAO1B;AACT;AAEA,OAAO,MAAMiB,MAAMpD,MAAM,eAAeoD,IACtCW,GAAyB,EACzBC,MAA6B;IAE7B,MAAMC,WAAWC,QAAQf,QAAQgB,GAAG,CAACC,WAAW;IAChD,MAAM,EACJC,aAAaC,eAAe,EAC5BC,gBAAgBC,kBAAkB,EAClCC,gBAAgBC,kBAAkB,EAClC,GAAGC,aACJ,GAAGZ,IAAIY,WAAW;IAEnB,MAAMC,yBAAyB,IAC7BxC,YACE2B,IAAI1B,aAAa,EACjB0B,IAAIzB,iBAAiB,EACrByB,IAAIc,YAAY,CAACtC,uBAAuB,EACxCwB,IAAIc,YAAY,CAACrC,eAAe;IAGpC,MAAMsC,oBAA8C;QAClD,qEAAqE;QACrE,wCAAwC;QACxC;YACE7E,QAAQyC,QAAQqC,OAAO,CAAC;YACxBC,SAAS;gBACPP,gBAAgBC;gBAChB,mEAAmE;gBACnE,4CAA4C;gBAC5CO,WAAW;gBACXN,aAAa;oBACX,sEAAsE;oBACtE,sEAAsE;oBACtE,wBAAwB;oBACxB,8GAA8G;oBAC9G,iDAAiD;oBACjD,oEAAoE;oBACpE,0BAA0B;oBAC1BO,QAAQ;oBACR,sDAAsD;oBACtDC,qBAAqB;wBAAC;qBAAgB;oBACtC,GAAGR,WAAW;gBAChB;gBACAJ,gBAAgBD,mBAAmBE;YACrC;QACF;QACA,yEAAyE;QACzE,uEAAuE;QACvE,WAAW;QACX,8DAA8D;QAC9D,8BAA8B;QAC9B;YACEvE,QAAQyC,QAAQqC,OAAO,CAAC;YACxBC,SAAS;gBACPvC,SAASmC;gBACT,6DAA6D;gBAC7D,QAAQ;gBACRK,WAAW;YACb;QACF;KACD;IAED,MAAMG,MAAyB,EAAE;IAEjC,MAAMC,eAAe3C,QAAQqC,OAAO,CAClC;IAEF,MAAMO,cAAc5C,QAAQqC,OAAO,CACjC;IAEF,MAAMQ,kBAA0D;QAC9D;YAAC7C,QAAQqC,OAAO,CAAC;YAAgCM;SAAa;QAC9D;YAAC3C,QAAQqC,OAAO,CAAC;YAA+BO;SAAY;KAC7D;IAEDC,gBAAgBC,OAAO,CAAC,CAAC,CAACC,kBAAkBC,eAAe;QACzD,uEAAuE;QACvEN,IAAIO,IAAI,CACN1F,OAAO;YACL2F,OAAO;gBACLjE,cAAc;oBACZkE,aAAa;oBACbC,MAAML;oBACNM,KAAKzF,kBAAkByD,KAAKa,wBAAwBc;gBACtD;aACD;QACH;IAEJ;IAEA,4EAA4E;IAC5E,gDAAgD;IAChDN,IAAIO,IAAI,CACN1F,OAAO;QACL2F,OAAO;YACLjE,cAAc;gBACZmE,MAAM/E;gBACN,iEAAiE;gBACjE,wCAAwC;gBACxCiF,QAAQ;gBACRD,KAAK;oBACH9F,QAAQ;oBACR+E,SAAS;wBACPiB,QAAQ1F;oBACV;gBACF;YACF;SACD;IACH;IAGF,MAAM2F,kCACJ,CAAC,CAACnC,IAAIc,YAAY,CAACsB,SAAS,IAAI,CAAC,CAACpC,IAAIqC,iBAAiB;IAEzD,mFAAmF;IACnFhB,IAAIO,IAAI,CACN,gEAAgE;IAChE,4DAA4D;IAC5D,iEAAiE;IACjE,uCAAuC;IACvC1F,OAAO;QACL2F,OAAO;YACL,wDAAwD;YACxD7B,IAAIsC,SAAS,GACT1E,cAAc;gBACZkE,aAAa;gBACbC,MAAM7E;gBACNqF,aAAalF;gBACb2E,KAAK;oBACH;wBACE9F,QAAQyC,QAAQqC,OAAO,CACrB;wBAEFC,SAAS;4BACPuB,YAAY;wBACd;oBACF;uBACGnG,mBACD;wBAAE,GAAG2D,GAAG;wBAAEyC,UAAU;oBAAK,GACzB5B;iBAEH;YACH,KACA;YACJjD,cAAc;gBACZkE,aAAa;gBACbC,MAAM7E;gBACNqF,aAAa7E;gBACbsE,KAAK3F,mBACH;oBAAE,GAAG2D,GAAG;oBAAEyC,UAAU;gBAAM,GAC1B5B;YAEJ;SACD,CAAC6B,MAAM,CAAC7F;IACX,IACA,6DAA6D;IAC7D,iEAAiE;IACjE,6DAA6D;IAC7D,kEAAkE;IAClE,uCAAuC;IACvCX,OAAO;QACL2F,OAAO;YACL,wDAAwD;YACxD7B,IAAIsC,SAAS,GACT1E,cAAc;gBACZkE,aAAa;gBACbC,MAAM3E;gBACNmF,aAAalF;gBACb2E,KAAK;oBACH;wBACE9F,QAAQyC,QAAQqC,OAAO,CACrB;wBAEFC,SAAS;4BACPuB,YAAY;wBACd;oBACF;uBACGnG,mBACD;wBAAE,GAAG2D,GAAG;wBAAEyC,UAAU;oBAAK,GACzB5B,wBACAE;iBAEH;YACH,KACA;YACJnD,cAAc;gBACZkE,aAAa;gBACbC,MAAM3E;gBACNmF,aAAa7E;gBACbsE,KAAK3F,mBACH;oBAAE,GAAG2D,GAAG;oBAAEyC,UAAU;gBAAM,GAC1B5B,wBACAE;YAEJ;SACD,CAAC2B,MAAM,CAAC7F;IACX,IACA,oEAAoE;IACpEX,OAAO;QACL2F,OAAO;YACLjE,cAAc;gBACZmE,MAAM;oBAAC7E;oBAAiBE;iBAAiB;gBACzC4E,KAAK;oBACH9F,QAAQ;oBACR+E,SAAS;wBACPiB,QAAQvF;oBACV;gBACF;YACF;SACD;IACH;IAGF,+BAA+B;IAC/B,IAAIqD,IAAI2C,QAAQ,EAAE;QAChBtB,IAAIO,IAAI,CACN1F,OAAO;YACL2F,OAAO;gBACL7B,IAAIsC,SAAS,IAAI,CAACtC,IAAI4C,YAAY,GAC9BhF,cAAc;oBACZkE,aAAa;oBACbC,MAAM;wBAAC9E;wBAAgBE;qBAAgB;oBACvCoF,aAAalF;oBACb2E,KAAK;wBACH9F,QAAQyC,QAAQqC,OAAO,CACrB;wBAEFC,SAAS;4BACPuB,YAAY;wBACd;oBACF;gBACF,KACA;gBACJ5E,cAAc;oBACZ,0DAA0D;oBAC1DkE,aAAa;oBACbC,MAAM;wBAAC9E;wBAAgBE;qBAAgB;oBACvC6E,KAAKrD,QAAQqC,OAAO,CAAC;gBACvB;aACD,CAAC0B,MAAM,CAAC7F;QACX;IAEJ,OAAO;QACL,iFAAiF;QACjF,yCAAyC;QACzC,iDAAiD;QACjD,iGAAiG;QACjG,gDAAgD;QAChD,MAAMgG,4BAA4B7C,IAAIsC,SAAS,GAC3CQ,YACA;YACEC,KAAK;gBACH;oBACEzF,IAAI;wBACF;wBACA;4BACEK,KAAK;gCAACqC,IAAI1B,aAAa;6BAAC;wBAC1B;qBACD;gBACH;aACD;QACH;QACJ,MAAM0E,8BAA8BhD,IAAIsC,SAAS,GAC7CQ,YACAX,kCACEW,YACA;YACEC,KAAK;gBAAC/C,IAAI1B,aAAa;aAAC;YACxBX,KAAK;gBAAC;aAAe;QACvB;QAEN0D,IAAIO,IAAI,CACN1F,OAAO;YACL2F,OAAO;mBACD7B,IAAIsC,SAAS,GACb;oBACE1E,cAAc;wBACZkE,aAAa;wBACbC,MAAM9E;wBACNsF,aAAalF;wBACb2E,KAAK;4BACH;gCACE9F,QAAQyC,QAAQqC,OAAO,CACrB;gCAEFC,SAAS;oCACPuB,YAAY;gCACd;4BACF;+BACGlG,mBACD;gCAAE,GAAG0D,GAAG;gCAAEyC,UAAU;4BAAK,GACzB5B;yBAEH;oBACH;oBACAjD,cAAc;wBACZkE,aAAa;wBACbC,MAAM5E;wBACNoF,aAAalF;wBACb2E,KAAK;4BACH;gCACE9F,QAAQyC,QAAQqC,OAAO,CACrB;gCAEFC,SAAS;oCACPuB,YAAY;gCACd;4BACF;+BACGlG,mBACD;gCAAE,GAAG0D,GAAG;gCAAEyC,UAAU;4BAAK,GACzB5B,wBACAE;yBAEH;oBACH;iBACD,GACD,EAAE;gBACNnD,cAAc;oBACZkE,aAAa;oBACbC,MAAM9E;oBACNgG,SAASJ;oBACTZ,QAAQe;oBACRT,aAAa7E;oBACbsE,KAAK1F,mBACH;wBAAE,GAAG0D,GAAG;wBAAEyC,UAAU;oBAAM,GAC1B5B;gBAEJ;gBACAjD,cAAc;oBACZkE,aAAa;oBACbC,MAAM5E;oBACN8F,SAASJ;oBACTZ,QAAQe;oBACRT,aAAa7E;oBACbsE,KAAK1F,mBACH;wBAAE,GAAG0D,GAAG;wBAAEyC,UAAU;oBAAM,GAC1B5B,wBACAE;gBAEJ;aACD,CAAC2B,MAAM,CAAC7F;QACX;QAGF,IAAImD,IAAIkD,aAAa,EAAE;YACrB7B,IAAIO,IAAI,CACN1F,OAAO;gBACL2F,OAAO;oBACLjE,cAAc;wBACZkE,aAAa;wBACbC,MAAM9E;wBACNgF,QAAQ;4BAAEc,KAAK;gCAAC/C,IAAIkD,aAAa;6BAAC;wBAAC;wBACnClB,KAAK1F,mBACH;4BAAE,GAAG0D,GAAG;4BAAEyC,UAAU;wBAAM,GAC1B5B;oBAEJ;iBACD;YACH,IACA3E,OAAO;gBACL2F,OAAO;oBACLjE,cAAc;wBACZkE,aAAa;wBACbC,MAAM5E;wBACN8E,QAAQ;4BAAEc,KAAK;gCAAC/C,IAAIkD,aAAa;6BAAC;wBAAC;wBACnClB,KAAK1F,mBACH;4BAAE,GAAG0D,GAAG;4BAAEyC,UAAU;wBAAM,GAC1B5B,wBACAE;oBAEJ;iBACD;YACH;QAEJ;IACF;IAEA,8DAA8D;IAC9D,IAAI,CAACoB,iCAAiC;QACpCd,IAAIO,IAAI,CACN1F,OAAO;YACL2F,OAAO;gBACLjE,cAAc;oBACZmE,MAAM;wBAAC9E;wBAAgBE;qBAAgB;oBACvC8E,QAAQ;wBAAEc,KAAK;4BAAC;yBAAe;oBAAC;oBAChCf,KAAK;wBACH9F,QAAQ;wBACR+E,SAAS;4BACPiB,QAAQxF;wBACV;oBACF;gBACF;aACD;QACH;IAEJ;IAEA,sEAAsE;IACtE2E,IAAIO,IAAI,CACN1F,OAAO;QACL2F,OAAO;YACLjE,cAAc;gBACZmE,MAAM;oBAAC9E;oBAAgBE;iBAAgB;gBACvC8E,QAAQjC,IAAIsC,SAAS,GACjB;oBACE,oEAAoE;oBACpE,kBAAkB;oBAClBS,KAAK;wBAAC/C,IAAI1B,aAAa;qBAAC;oBACxBX,KAAK;wBAAC;qBAA+B;gBACvC,IACAmF;gBACJd,KAAK;oBACH9F,QAAQ;oBACR+E,SAAS;wBACPiB,QAAQzF;oBACV;gBACF;YACF;SACD;IACH;IAGF,IAAIuD,IAAImD,QAAQ,EAAE;QAChB,qEAAqE;QACrE,uBAAuB;QACvB9B,IAAIO,IAAI,CACN1F,OAAO;YACL2F,OAAO;gBACLjE,cAAc;oBACZ,2CAA2C;oBAC3CqE,QAAQjF;oBACR,qDAAqD;oBACrDoG,SAAS;wBACP;wBACA;wBACA;wBACA;qBACD;oBACD,+DAA+D;oBAC/D,uCAAuC;oBACvCC,MAAM;gBACR;aACD;QACH;IAEJ;IAEA,yEAAyE;IACzE,IAAIrD,IAAImD,QAAQ,IAAKnD,CAAAA,IAAI4C,YAAY,IAAI5C,IAAIsC,SAAS,AAAD,GAAI;QACvD,mEAAmE;QACnE,MAAMgB,uBAAuBpD,WACzBnD,gBAAgBwG,sBAAsB,GACtC5E,QAAQ,4CAA4C6E,OAAO;QAE/DnC,IAAIO,IAAI,CACNzF,OACE,8BAA8B;QAC9B,IAAImH,qBAAqB;YACvBG,UAAUzD,IAAI4C,YAAY,GACtB,iCACA;YACJc,eAAe1D,IAAI4C,YAAY,GAC3B,iCACA;YACJ,qEAAqE;YACrE,gBAAgB;YAChB,kEAAkE;YAClE,mEAAmE;YACnE,8CAA8C;YAC9C,EAAE;YACF,iEAAiE;YACjE,4DAA4D;YAC5D,EAAE;YACF,qEAAqE;YACrE,4CAA4C;YAC5Ce,aAAa;YACbC,QAAQ,SAAUC,OAAwB;gBACxC,IAAI,OAAOC,oBAAoB,YAAY;oBACzC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE,GAAGJ;oBAClCC,gBACEC,KAAKG,OAAO,CAACC,OAAOC,QAAQ,CAACC,MAAM,MAAM,IACrC,IAAIC,IAAIP,MAAMQ,QAAQ,GACtBR,MACJS,IAAI,CACJ,IAAMR,0BAAAA,OAAQS,IAAI,CAACZ,SAAS;4BAAER,MAAM;wBAAO,IAC3C,IAAMY,2BAAAA,QAASQ,IAAI,CAACZ,SAAS,CAAC;gBAElC,OAAO;oBACLa,SAASC,IAAI,CAACC,WAAW,CAACf;gBAC5B;YACF;QACF;IAGN;IAEA,MAAMgB,KAAKzI,QAAQiF;IACnB,OAAOwD,GAAG5E;AACZ,GAAE"}