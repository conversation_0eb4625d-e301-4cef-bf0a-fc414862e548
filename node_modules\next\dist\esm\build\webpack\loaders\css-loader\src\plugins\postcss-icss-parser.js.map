{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-icss-parser.ts"], "sourcesContent": ["import {\n  extractICSS,\n  replaceValueSymbols,\n  replaceSymbols,\n} from 'next/dist/compiled/icss-utils'\n\nimport { normalizeUrl, resolveRequests, requestify } from '../utils'\n\nconst plugin = (options: any = {}) => {\n  return {\n    postcssPlugin: 'postcss-icss-parser',\n    async OnceExit(root: any) {\n      const importReplacements = Object.create(null)\n      const { icssImports, icssExports } = extractICSS(root)\n      const imports = new Map()\n      const tasks = []\n\n      // eslint-disable-next-line guard-for-in\n      for (const url in icssImports) {\n        const tokens = icssImports[url]\n\n        if (Object.keys(tokens).length === 0) {\n          // eslint-disable-next-line no-continue\n          continue\n        }\n\n        let normalizedUrl = url\n        let prefix = ''\n\n        const queryParts = normalizedUrl.split('!')\n\n        if (queryParts.length > 1) {\n          normalizedUrl = queryParts.pop()!\n          prefix = queryParts.join('!')\n        }\n\n        const request = requestify(\n          normalizeUrl(normalizedUrl, true),\n          options.rootContext\n        )\n        const doResolve = async () => {\n          const { resolver, context } = options\n          const resolvedUrl = await resolveRequests(resolver, context, [\n            ...new Set([normalizedUrl, request]),\n          ])\n\n          if (!resolvedUrl) {\n            return\n          }\n\n          // eslint-disable-next-line consistent-return\n          return { url: resolvedUrl, prefix, tokens }\n        }\n\n        tasks.push(doResolve())\n      }\n\n      const results = await Promise.all(tasks)\n\n      for (let index = 0; index <= results.length - 1; index++) {\n        const item = results[index]\n\n        if (!item) {\n          // eslint-disable-next-line no-continue\n          continue\n        }\n\n        const newUrl = item.prefix ? `${item.prefix}!${item.url}` : item.url\n        const importKey = newUrl\n        let importName = imports.get(importKey)\n\n        if (!importName) {\n          importName = `___CSS_LOADER_ICSS_IMPORT_${imports.size}___`\n          imports.set(importKey, importName)\n\n          options.imports.push({\n            type: 'icss_import',\n            importName,\n            url: options.urlHandler(newUrl),\n            icss: true,\n            index,\n          })\n\n          options.api.push({ importName, dedupe: true, index })\n        }\n\n        for (const [replacementIndex, token] of Object.keys(\n          item.tokens\n        ).entries()) {\n          const replacementName = `___CSS_LOADER_ICSS_IMPORT_${index}_REPLACEMENT_${replacementIndex}___`\n          const localName = item.tokens[token]\n\n          importReplacements[token] = replacementName\n\n          options.replacements.push({ replacementName, importName, localName })\n        }\n      }\n\n      if (Object.keys(importReplacements).length > 0) {\n        replaceSymbols(root, importReplacements)\n      }\n\n      for (const name of Object.keys(icssExports)) {\n        const value = replaceValueSymbols(icssExports[name], importReplacements)\n\n        options.exports.push({ name, value })\n      }\n    },\n  }\n}\n\nplugin.postcss = true\n\nexport default plugin\n"], "names": ["extractICSS", "replaceValueSymbols", "replaceSymbols", "normalizeUrl", "resolveRequests", "requestify", "plugin", "options", "postcssPlugin", "OnceExit", "root", "importReplacements", "Object", "create", "icssImports", "icssExports", "imports", "Map", "tasks", "url", "tokens", "keys", "length", "normalizedUrl", "prefix", "queryParts", "split", "pop", "join", "request", "rootContext", "doResolve", "resolver", "context", "resolvedUrl", "Set", "push", "results", "Promise", "all", "index", "item", "newUrl", "importKey", "importName", "get", "size", "set", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icss", "api", "dedupe", "replacementIndex", "token", "entries", "replacement<PERSON>ame", "localName", "replacements", "name", "value", "exports", "postcss"], "mappings": "AAAA,SACEA,WAAW,EACXC,mBAAmB,EACnBC,cAAc,QACT,gCAA+B;AAEtC,SAASC,YAAY,EAAEC,eAAe,EAAEC,UAAU,QAAQ,WAAU;AAEpE,MAAMC,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACf,MAAMC,UAASC,IAAS;YACtB,MAAMC,qBAAqBC,OAAOC,MAAM,CAAC;YACzC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAAGf,YAAYU;YACjD,MAAMM,UAAU,IAAIC;YACpB,MAAMC,QAAQ,EAAE;YAEhB,wCAAwC;YACxC,IAAK,MAAMC,OAAOL,YAAa;gBAC7B,MAAMM,SAASN,WAAW,CAACK,IAAI;gBAE/B,IAAIP,OAAOS,IAAI,CAACD,QAAQE,MAAM,KAAK,GAAG;oBAEpC;gBACF;gBAEA,IAAIC,gBAAgBJ;gBACpB,IAAIK,SAAS;gBAEb,MAAMC,aAAaF,cAAcG,KAAK,CAAC;gBAEvC,IAAID,WAAWH,MAAM,GAAG,GAAG;oBACzBC,gBAAgBE,WAAWE,GAAG;oBAC9BH,SAASC,WAAWG,IAAI,CAAC;gBAC3B;gBAEA,MAAMC,UAAUxB,WACdF,aAAaoB,eAAe,OAC5BhB,QAAQuB,WAAW;gBAErB,MAAMC,YAAY;oBAChB,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAG1B;oBAC9B,MAAM2B,cAAc,MAAM9B,gBAAgB4B,UAAUC,SAAS;2BACxD,IAAIE,IAAI;4BAACZ;4BAAeM;yBAAQ;qBACpC;oBAED,IAAI,CAACK,aAAa;wBAChB;oBACF;oBAEA,6CAA6C;oBAC7C,OAAO;wBAAEf,KAAKe;wBAAaV;wBAAQJ;oBAAO;gBAC5C;gBAEAF,MAAMkB,IAAI,CAACL;YACb;YAEA,MAAMM,UAAU,MAAMC,QAAQC,GAAG,CAACrB;YAElC,IAAK,IAAIsB,QAAQ,GAAGA,SAASH,QAAQf,MAAM,GAAG,GAAGkB,QAAS;gBACxD,MAAMC,OAAOJ,OAAO,CAACG,MAAM;gBAE3B,IAAI,CAACC,MAAM;oBAET;gBACF;gBAEA,MAAMC,SAASD,KAAKjB,MAAM,GAAG,GAAGiB,KAAKjB,MAAM,CAAC,CAAC,EAAEiB,KAAKtB,GAAG,EAAE,GAAGsB,KAAKtB,GAAG;gBACpE,MAAMwB,YAAYD;gBAClB,IAAIE,aAAa5B,QAAQ6B,GAAG,CAACF;gBAE7B,IAAI,CAACC,YAAY;oBACfA,aAAa,CAAC,0BAA0B,EAAE5B,QAAQ8B,IAAI,CAAC,GAAG,CAAC;oBAC3D9B,QAAQ+B,GAAG,CAACJ,WAAWC;oBAEvBrC,QAAQS,OAAO,CAACoB,IAAI,CAAC;wBACnBY,MAAM;wBACNJ;wBACAzB,KAAKZ,QAAQ0C,UAAU,CAACP;wBACxBQ,MAAM;wBACNV;oBACF;oBAEAjC,QAAQ4C,GAAG,CAACf,IAAI,CAAC;wBAAEQ;wBAAYQ,QAAQ;wBAAMZ;oBAAM;gBACrD;gBAEA,KAAK,MAAM,CAACa,kBAAkBC,MAAM,IAAI1C,OAAOS,IAAI,CACjDoB,KAAKrB,MAAM,EACXmC,OAAO,GAAI;oBACX,MAAMC,kBAAkB,CAAC,0BAA0B,EAAEhB,MAAM,aAAa,EAAEa,iBAAiB,GAAG,CAAC;oBAC/F,MAAMI,YAAYhB,KAAKrB,MAAM,CAACkC,MAAM;oBAEpC3C,kBAAkB,CAAC2C,MAAM,GAAGE;oBAE5BjD,QAAQmD,YAAY,CAACtB,IAAI,CAAC;wBAAEoB;wBAAiBZ;wBAAYa;oBAAU;gBACrE;YACF;YAEA,IAAI7C,OAAOS,IAAI,CAACV,oBAAoBW,MAAM,GAAG,GAAG;gBAC9CpB,eAAeQ,MAAMC;YACvB;YAEA,KAAK,MAAMgD,QAAQ/C,OAAOS,IAAI,CAACN,aAAc;gBAC3C,MAAM6C,QAAQ3D,oBAAoBc,WAAW,CAAC4C,KAAK,EAAEhD;gBAErDJ,QAAQsD,OAAO,CAACzB,IAAI,CAAC;oBAAEuB;oBAAMC;gBAAM;YACrC;QACF;IACF;AACF;AAEAtD,OAAOwD,OAAO,GAAG;AAEjB,eAAexD,OAAM"}